<template>
  <div class="p-4">
    <h2>CustomModal 简单测试</h2>
    
    <div class="test-buttons">
      <a-button type="primary" @click="showModal">显示弹窗</a-button>
    </div>

    <!-- 测试弹窗 -->
    <CustomModal
      v-model:open="visible"
      title="自主委托"
      width="600"
      @confirm="handleConfirm"
      @cancel="handleCancel"
      @close="handleClose"
    >
      <div class="modal-content">
        <p>我方仅将客户资产信息在我网进行公示，我司不会对客户所发布信息做任何协助</p>
        <p>请确认是否选择自主服务</p>
      </div>
    </CustomModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CustomModal } from '/@/components/Modal'

const visible = ref(false)

const showModal = () => {
  visible.value = true
}

const handleConfirm = () => {
  console.log('确认')
  visible.value = false
}

const handleCancel = () => {
  console.log('取消')
}

const handleClose = () => {
  console.log('关闭')
}
</script>

<style lang="less" scoped>
.test-buttons {
  margin: 20px 0;
}

.modal-content {
  text-align: center;
  padding: 20px 0;

  p {
    margin: 12px 0;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
  }
}

.p-4 {
  padding: 16px;
}
</style>
