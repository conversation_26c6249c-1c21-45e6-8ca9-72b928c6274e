<template>
  <PageWrapper title="Tiptap字体演示">
    <div class="p-4">
      <a-card title="字体展示">
        <div class="mb-4">
          <a-alert
            message="字体演示说明"
            description="以下展示了Tiptap富文本编辑器支持的所有字体，包括系统字体和项目本地字体。点击下方编辑器中的字体下拉菜单可以选择这些字体。"
            type="info"
            show-icon
          />
        </div>

        <!-- 字体展示区域 -->
        <div class="font-showcase">
          <h3>系统字体</h3>
          <div class="font-group">
            <div class="font-item">
              <div class="font-name">默认字体</div>
              <div class="font-sample" style="font-family: inherit;">
                这是默认字体的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">宋体 (SimSun)</div>
              <div class="font-sample" style="font-family: SimSun;">
                这是宋体的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">黑体 (SimHei)</div>
              <div class="font-sample" style="font-family: SimHei;">
                这是黑体的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">微软雅黑 (Microsoft YaHei)</div>
              <div class="font-sample" style="font-family: Microsoft YaHei;">
                这是微软雅黑的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">楷体 (KaiTi)</div>
              <div class="font-sample" style="font-family: KaiTi;">
                这是楷体的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">仿宋 (FangSong)</div>
              <div class="font-sample" style="font-family: FangSong;">
                这是仿宋的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
          </div>

          <h3>苹方字体系列</h3>
          <div class="font-group">
            <div class="font-item">
              <div class="font-name">苹方常规 (PingFang Regular)</div>
              <div class="font-sample" style="font-family: 'PingFang Regular';">
                这是苹方常规的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">苹方中等 (PingFang Medium)</div>
              <div class="font-sample" style="font-family: 'PingFang Medium';">
                这是苹方中等的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">苹方加粗 (PingFang Bold)</div>
              <div class="font-sample" style="font-family: 'PingFang Bold';">
                这是苹方加粗的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">苹方特粗 (PingFang Heavy)</div>
              <div class="font-sample" style="font-family: 'PingFang Heavy';">
                这是苹方特粗的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
          </div>

          <h3>DIN字体系列</h3>
          <div class="font-group">
            <div class="font-item">
              <div class="font-name">DIN常规 (DIN Regular)</div>
              <div class="font-sample" style="font-family: 'DIN Regular';">
                这是DIN常规的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">DIN加粗 (DIN Bold)</div>
              <div class="font-sample" style="font-family: 'DIN Bold';">
                这是DIN加粗的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">DIN黑斜体 (DIN BlackItalic)</div>
              <div class="font-sample" style="font-family: 'DIN BlackItalic';">
                这是DIN黑斜体的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
          </div>

          <h3>特殊字体</h3>
          <div class="font-group">
            <div class="font-item">
              <div class="font-name">方正综艺 (FZZongYi-M05S)</div>
              <div class="font-sample" style="font-family: 'FZZongYi-M05S';">
                这是方正综艺的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">优设标题黑 (YouSheBiaoTiHei)</div>
              <div class="font-sample" style="font-family: 'YouSheBiaoTiHei';">
                这是优设标题黑的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
          </div>

          <h3>英文字体</h3>
          <div class="font-group">
            <div class="font-item">
              <div class="font-name">Arial</div>
              <div class="font-sample" style="font-family: Arial;">
                这是Arial的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">Times New Roman</div>
              <div class="font-sample" style="font-family: 'Times New Roman';">
                这是Times New Roman的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
            <div class="font-item">
              <div class="font-name">Courier New</div>
              <div class="font-sample" style="font-family: 'Courier New';">
                这是Courier New的演示文本 - The quick brown fox jumps over the lazy dog
              </div>
            </div>
          </div>
        </div>
      </a-card>

      <a-card title="实际编辑测试" class="mt-4">
        <div class="mb-4">
          <a-alert
            message="编辑器测试"
            description="在下方编辑器中测试字体选择功能。点击工具栏中的字体下拉菜单，选择不同的字体进行测试。"
            type="success"
            show-icon
          />
        </div>

        <TiptapEditor
          v-model="content"
          height="300px"
          placeholder="请在这里测试字体选择功能..."
          @change="handleChange"
        />

        <div class="mt-4">
          <h4>当前内容：</h4>
          <div class="content-preview" v-html="content"></div>
        </div>
      </a-card>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { PageWrapper } from '/@/components/Page'
import { TiptapEditor } from '/@/components/TiptapEditor'

const content = ref(`
<p style="font-family: 'PingFang Medium'; font-size: 16px;">
  欢迎测试字体功能！请选择不同的字体来体验效果。
</p>
`)

const handleChange = (value: string) => {
  console.log('内容变化:', value)
}
</script>

<style lang="less" scoped>
.font-showcase {
  h3 {
    margin: 24px 0 16px 0;
    color: #1890ff;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 8px;
    
    &:first-child {
      margin-top: 0;
    }
  }
}

.font-group {
  margin-bottom: 24px;
}

.font-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
  
  &:hover {
    border-color: #1890ff;
    background: #f6ffed;
  }
}

.font-name {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
}

.font-sample {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  padding: 8px 0;
}

.content-preview {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  background: #fff;
  min-height: 60px;
  
  :deep(p) {
    margin: 8px 0;
    
    &:first-child {
      margin-top: 0;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.p-4 {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
