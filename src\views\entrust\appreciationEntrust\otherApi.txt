增值委托中发布资产处置接口为：/hgy/entrustService/hgyAssetEntrust/addDraft
{
  /*委托企业ID，在自主模式下为对应处置单位 */
  entrustCompanyId: number;
  /*受委托企业ID */
  onEntrustCompanyId: number;
  /*委托类型(1-增值 2-自主) */
  entrustType: number;
  /*服务类型(1-竞价委托 2-资产处置 3-采购信息) */
  serviceType: number;
  /*状态(1-草稿 2-提交 ) */
  status: number;
   /*资产名称 */
  assetName: string;
  /*资产编号 */
  assetNo?: string;
  /*资产类型 */
  assetType: number;
  /*资产数量 */
  quantity: string;
  /*计量单位 */
  unit: string;
  /*省份 */
  provinceCode: string;
  /*城市编码 */
  cityCode: string;
  /*区县编码 */
  districtCode: string;
  /*详细地址 */
  address: string;
  /*联系人 */
  relationUser?: string;
  /*联系电话 */
  relationPhone?: string;
  attachmentList?: {
    /*业务类型 */
    bizType?: string;
    /*文件名称 */
    fileName?: string;
    /*文件路径 */
    filePath?: string;
    /*文件大小(字节) */
    fileSize?: number;
    /*文件类型 */
    fileType?: string;
  }[];
}

增值委托中发布采购信息接口为：/hgy/entrustService/hgyProcurement/addDraft
{
  /*委托类型(1-增值 2-自主) */
  entrustType: number;
  /*服务类型(1-竞价委托 2-资产处置 3-采购信息) */
  serviceType: number;
  /*状态(1-草稿 2-提交 ) */
  status: number;
  /*公告名称 */
  noticeName: string;
  /*计量单位 */
  unit: string;
  /*省份 */
  provinceCode: string;
  /*城市编码 */
  cityCode: string;
  /*区县编码 */
  districtCode: string;
  /*详细地址 */
  address: string;
  /*联系人 */
    relationUser?: string;
  /*联系电话 */
  relationPhone?: string;
  attachmentList?: {
    /*业务类型 */
    bizType?: string;
    /*文件名称 */
    fileName?: string;
    /*文件路径 */
    filePath?: string;
    /*文件大小(字节) */
    fileSize?: number;
    /*文件类型 */
    fileType?: string;
  }[];
}
