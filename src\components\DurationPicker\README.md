# DurationPicker 时间长度选择器

一个用于选择时间长度的Vue组件，支持选择天、小时、分钟、秒。

## 特性

- ✅ **直观选择**：通过下拉选择框选择时间长度
- ✅ **多单位支持**：支持天、小时、分钟、秒
- ✅ **格式化输出**：自动格式化为中文描述，如"1天2小时3分钟4秒"
- ✅ **响应式**：支持v-model双向绑定
- ✅ **多尺寸**：支持small、default、large三种尺寸
- ✅ **业务友好**：天数限制在0-10天，适合拍卖等业务场景

## 基础使用

```vue
<template>
  <DurationPicker v-model:value="duration" placeholder="请选择时长" />
</template>

<script setup>
import { ref } from 'vue'
import DurationPicker from '@/components/DurationPicker'

const duration = ref('')
</script>
```

## 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | string | '' | 时长值，格式如"1天2小时3分钟4秒" |
| placeholder | string | '请选择时长' | 占位符文本 |
| size | 'small' \| 'default' \| 'large' | 'default' | 组件尺寸 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:value | (value: string) | v-model更新事件 |
| change | (value: string) | 值变化时触发 |

## 选择范围

- **天数**：0-10天
- **小时**：0-23小时
- **分钟**：0-59分钟
- **秒数**：0-59秒

## 数据格式

组件输出的时长格式为中文描述：
- 输入：选择1天、2小时、30分钟、0秒
- 输出：`"1天2小时30分钟"`（值为0的单位会被省略）

## 尺寸示例

```vue
<template>
  <!-- 小尺寸 -->
  <DurationPicker v-model:value="duration1" size="small" />
  
  <!-- 默认尺寸 -->
  <DurationPicker v-model:value="duration2" />
  
  <!-- 大尺寸 -->
  <DurationPicker v-model:value="duration3" size="large" />
</template>
```

## 在表单中使用

```vue
<template>
  <a-form :model="formData">
    <a-form-item label="竞价时长" name="bidDuration">
      <DurationPicker 
        v-model:value="formData.bidDuration" 
        placeholder="请选择竞价时长"
        size="large"
      />
    </a-form-item>
  </a-form>
</template>
```

## 样式定制

组件使用CSS类名进行样式控制：
- `.duration-picker` - 主容器
- `.duration-picker-small` - 小尺寸样式
- `.duration-picker-large` - 大尺寸样式
- `.duration-inputs` - 输入区域容器
- `.duration-input-group` - 单个输入组
- `.duration-unit` - 单位文本

可以通过覆盖这些类名来自定义样式。
