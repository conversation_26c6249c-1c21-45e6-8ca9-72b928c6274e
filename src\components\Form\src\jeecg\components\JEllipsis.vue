<template>
  <a-tooltip placement="topLeft">
    <template #title>
      <span>{{ value }}</span>
    </template>
    {{ showText }}
  </a-tooltip>
</template>
<script lang="ts" setup>
  import { computed } from 'vue';
  import { propTypes } from '/@/utils/propTypes';
  import { regionData } from '/@/components/Form/src/utils/areaDataUtil';


  const props = defineProps({
    value: propTypes.oneOfType([propTypes.string, propTypes.number, propTypes.array]),
    length: propTypes.number.def(25),
    // 新增属性：是否为省市区code数组
    isAreaCode: propTypes.bool.def(false),
  });
  //显示的文本 原本的代码
  /* const showText = computed(() =>
    props.value ? (props.value.length > props.length ? props.value.slice(0, props.length) + '...' : props.value) : props.value
  ); */

  // 新加的代码
  /**
   * 将省市区code数组转换为文字数组
   * @param codes - 省市区code数组，如：['130000', '130200']
   * @returns 返回对应的文字数组，如：['河北省', '唐山市']
   */
  function getAreaTextByCodes(codes) {
    if (!codes || !Array.isArray(codes) || codes.length === 0) {
      return [];
    }
    
    const textArray = [];
    
    // 按照层级顺序查找对应的文字
    for (let i = 0; i < codes.length; i++) {
      const code = codes[i];
      let found = false;
      
      // 查找省份
      if (i === 0) {
        for (const province of regionData) {
          if (province.value === code) {
            textArray.push(province.label);
            found = true;
            break;
          }
        }
      }
      // 查找市
      else if (i === 1) {
        const provinceCode = codes[0];
        const province = regionData.find(p => p.value === provinceCode);
        if (province && province.children) {
          for (const city of province.children) {
            if (city.value === code) {
              textArray.push(city.label);
              found = true;
              break;
            }
          }
        }
      }
      // 查找区县
      else if (i === 2) {
        const provinceCode = codes[0];
        const cityCode = codes[1];
        const province = regionData.find(p => p.value === provinceCode);
        if (province && province.children) {
          const city = province.children.find(c => c.value === cityCode);
          if (city && city.children) {
            for (const area of city.children) {
              if (area.value === code) {
                textArray.push(area.label);
                found = true;
                break;
              }
            }
          }
        }
      }
      
      // 如果没找到，显示原始code
      if (!found) {
        textArray.push(code);
      }
    }
    
    return textArray;
  }

  // 获取要显示的文本内容
  const displayValue = computed(() => {
    if (!props.value) return props.value;
    
    // 如果是省市区code数组，转换为文字显示
    if (props.isAreaCode && Array.isArray(props.value)) {
      const textArray = getAreaTextByCodes(props.value);
      return textArray.join(' / ');
    }
    
    // 如果是数组但不是省市区code，直接join显示
    if (Array.isArray(props.value)) {
      return props.value.join(', ');
    }
    
    // 其他情况直接返回
    return props.value;
  });

  //显示的文本（处理长度截断）
  const showText = computed(() => {
    const text = displayValue.value;
    if (!text) return text;
    
    const textStr = String(text);
    console.log('text',textStr)
    return textStr.length > props.length ? textStr.slice(0, props.length) + '...' : textStr;
  });
</script>
