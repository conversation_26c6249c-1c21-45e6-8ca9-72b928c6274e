<template>
  <div class="step1">
    <a-form :model="formData" :rules="rules" ref="formRef" :scroll-to-first-error="true">
      <!-- 服务类型板块 -->
      <div class="form-section">
        <h3 class="section-title">服务类型</h3>
        <div class="form-row">
          <a-form-item name="serviceType" class="service-type-item">
            <a-select
              v-model:value="formData.serviceType"
              @change="handleServiceTypeChange"
              placeholder="请选择服务类型"
              size="large"
              class="service-type-select"
              :disabled="props.isEditMode"
            >
              <a-select-option :value="2">发布资产处置</a-select-option>
              <a-select-option :value="3">发布采购信息</a-select-option>
            </a-select>
            <div v-if="props.isEditMode" class="edit-mode-tip">
              <span class="tip-text">编辑模式下不允许修改服务类型</span>
            </div>
          </a-form-item>
        </div>
      </div>

      <!-- 关联委托单号板块 -->
      <div class="form-section">
        <h3 class="section-title">关联委托单号</h3>
        <div class="form-row">
          <a-form-item name="entrustOrderId" class="entrust-order-item">
            <a-select
              v-model:value="formData.entrustOrderId"
              placeholder="请选择关联委托单号"
              size="large"
              class="entrust-order-select"
              :loading="entrustOrderLoading"
              show-search
              :filter-option="filterOption"
            >
              <a-select-option v-for="item in entrustOrderList" :key="item.id" :value="item.id" :label="item.entrustOrderNo">
                {{ item.entrustOrderNo }} - {{ item.entrustCompanyName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </div>

      <!-- 委托信息板块 -->
      <div class="form-section">
        <div class="section-title">委托信息</div>
        <div class="form-row entrust-two-row">
          <a-form-item label="委托单位" :name="['entrustInfo', 'title']" required class="entrust-two-item">
            <a-input v-model:value="formData.entrustInfo.title" placeholder="委托单位" size="large" disabled readonly />
          </a-form-item>
          <a-form-item label="受委托单位" :name="['entrustInfo', 'type']" required class="entrust-two-item">
            <a-input v-model:value="formData.entrustInfo.type" placeholder="受委托单位" size="large" disabled readonly />
          </a-form-item>
        </div>

        <!-- 采购信息特有字段 -->
        <div v-if="formData.serviceType === 3" class="form-row">
          <a-form-item label="公告名称" :name="['entrustInfo', 'noticeName']" required class="full-width-item">
            <a-input v-model:value="formData.entrustInfo.noticeName" placeholder="请输入公告名称" size="large" />
          </a-form-item>
        </div>
      </div>

      <!-- 基本信息板块（仅发布资产处置显示） -->
      <div v-if="formData.serviceType === 2" class="form-section">
        <div class="section-title">基本信息</div>
        <!-- 第一行：资产名称、资产编号、资产类型 -->
        <div class="form-row basic-three-row">
          <a-form-item label="资产名称" :name="['basicInfo', 'assetName']" required class="basic-three-item">
            <a-input v-model:value="formData.basicInfo.assetName" placeholder="请输入资产名称" size="large" />
          </a-form-item>
          <a-form-item label="资产编号" :name="['basicInfo', 'assetNo']" class="basic-three-item">
            <a-input v-model:value="formData.basicInfo.assetNo" placeholder="请输入资产编号" size="large" />
          </a-form-item>
          <a-form-item label="资产类型" :name="['basicInfo', 'assetType']" required class="basic-three-item">
            <a-select v-model:value="formData.basicInfo.assetType" placeholder="请选择资产类型" size="large">
              <a-select-option :value="1">物资/设备</a-select-option>
              <a-select-option :value="2">机动车</a-select-option>
              <a-select-option :value="3">房产</a-select-option>
              <a-select-option :value="4">土地</a-select-option>
              <a-select-option :value="5">其他</a-select-option>
            </a-select>
          </a-form-item>
        </div>

        <!-- 第二行：资产数量、计量单位、是否展示实际数量 -->
        <div class="form-row basic-three-row">
          <a-form-item label="资产数量" :name="['basicInfo', 'quantity']" required class="basic-three-item">
            <a-input v-model:value="formData.basicInfo.quantity" placeholder="请输入资产数量" size="large" />
          </a-form-item>
          <a-form-item label="计量单位" :name="['basicInfo', 'unit']" required class="basic-three-item">
            <a-input v-model:value="formData.basicInfo.unit" placeholder="请输入计量单位" size="large" />
          </a-form-item>
          <a-form-item label="是否展示实际数量" :name="['basicInfo', 'quantityFlag']" class="basic-three-item">
            <a-radio-group v-model:value="formData.basicInfo.quantityFlag">
              <a-radio :value="0">否</a-radio>
              <a-radio :value="1">是</a-radio>
            </a-radio-group>
          </a-form-item>
        </div>

        <!-- 第三行：使用年限、新旧程度、当前状态 -->
        <div class="form-row basic-three-row">
          <a-form-item label="使用年限" :name="['basicInfo', 'serviceLife']" class="basic-three-item">
            <a-input-number
              v-model:value="formData.basicInfo.serviceLife"
              placeholder="请输入使用年限"
              size="large"
              :min="0"
              style="width: 100%"
              :precision="0"
            />
          </a-form-item>
          <a-form-item label="新旧程度" :name="['basicInfo', 'depreciationDegree']" class="basic-three-item">
            <a-select v-model:value="formData.basicInfo.depreciationDegree" placeholder="请选择新旧程度" size="large">
              <a-select-option :value="1">九成新</a-select-option>
              <a-select-option :value="2">八成新</a-select-option>
              <a-select-option :value="3">七成新</a-select-option>
              <a-select-option :value="4">六成新</a-select-option>
              <a-select-option :value="5">五成新</a-select-option>
              <a-select-option :value="6">四成新</a-select-option>
              <a-select-option :value="7">三成新</a-select-option>
              <a-select-option :value="8">二成新</a-select-option>
              <a-select-option :value="9">一成新</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="当前状态" :name="['basicInfo', 'currentStatus']" class="basic-three-item">
            <a-select v-model:value="formData.basicInfo.currentStatus" placeholder="请选择当前状态" size="large">
              <a-select-option :value="1">在用</a-select-option>
              <a-select-option :value="2">闲置</a-select-option>
              <a-select-option :value="3">报废</a-select-option>
            </a-select>
          </a-form-item>
        </div>

        <!-- 第四行：评估价值、处置底价 -->
        <div class="form-row basic-two-row">
          <a-form-item label="评估价值" :name="['basicInfo', 'appraisalValue']" class="basic-two-item">
            <a-input-number
              v-model:value="formData.basicInfo.appraisalValue"
              placeholder="请输入评估价值"
              size="large"
              :min="0"
              style="width: 100%"
              :precision="2"
            />
          </a-form-item>
          <a-form-item label="处置底价" :name="['basicInfo', 'disposalPrice']" class="basic-two-item">
            <a-input-number
              v-model:value="formData.basicInfo.disposalPrice"
              placeholder="请输入处置底价"
              size="large"
              :min="0"
              style="width: 100%"
              :precision="2"
            />
          </a-form-item>
        </div>

        <!-- 第五行：处置时间 -->
        <div class="form-row basic-two-row">
          <a-form-item label="处置开始时间" :name="['basicInfo', 'disposalStartTime']" class="basic-two-item">
            <a-date-picker
              v-model:value="formData.basicInfo.disposalStartTime"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择处置开始时间"
              size="large"
              style="width: 100%"
            />
          </a-form-item>
          <a-form-item label="处置结束时间" :name="['basicInfo', 'disposalEndTime']" class="basic-two-item">
            <a-date-picker
              v-model:value="formData.basicInfo.disposalEndTime"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择处置结束时间"
              size="large"
              style="width: 100%"
            />
          </a-form-item>
        </div>

        <!-- 第六行：付款方式、是否含税 -->
        <div class="form-row basic-two-row">
          <a-form-item label="付款方式" :name="['basicInfo', 'paymentMethod']" class="basic-two-item">
            <a-radio-group v-model:value="formData.basicInfo.paymentMethod">
              <a-radio :value="1">全款</a-radio>
              <a-radio :value="2">分期</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="是否含税" :name="['basicInfo', 'isTaxIncluded']" class="basic-two-item">
            <a-select v-model:value="formData.basicInfo.isTaxIncluded" placeholder="请选择是否含税" size="large">
              <a-select-option value="0">不含税</a-select-option>
              <a-select-option value="3">含税3%</a-select-option>
              <a-select-option value="6">含税6%</a-select-option>
              <a-select-option value="9">含税9%</a-select-option>
              <a-select-option value="13">含税13%</a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </div>

      <!-- 存放位置板块 -->
      <div class="form-section">
        <div class="section-title">存放位置</div>
        <div class="form-row location-row">
          <a-form-item label="省市区" :name="['location', 'province']" required class="location-cascader-item">
            <JAreaSelect
              v-model:province="formData.location.province"
              v-model:city="formData.location.city"
              v-model:area="formData.location.area"
              @change="handleAreaChange"
              :level="3"
            />
          </a-form-item>
          <a-form-item label="详细地址" :name="['location', 'detailAddress']" required class="location-address-item">
            <div class="address-input-wrapper">
              <a-input v-model:value="formData.location.detailAddress" placeholder="请输入详细地址" size="large" class="address-input" />
              <a-button type="link" class="location-btn" :loading="props.locationLoading" @click="handleGetCurrentLocation">
                <EnvironmentOutlined />
                获取当前位置
              </a-button>
            </div>
          </a-form-item>
        </div>
      </div>

      <!-- 资料上传板块 -->
      <div class="form-section">
        <div class="section-title">资料上传</div>
        <div class="form-row">
          <a-form-item label="资产图片" :name="['other', 'images']" class="upload-item">
            <JUpload
              v-model:value="formData.other.images"
              :max-count="10"
              accept="image/*"
              list-type="picture-card"
              upload-text="上传图片"
              :file-size="10"
              :existing-files="formData.hgyAttachmentList"
              biz-type="images"
            />
          </a-form-item>
        </div>

        <div class="form-row">
          <a-form-item label="附件上传" :name="['other', 'attachments']" class="upload-item">
            <JUpload
              v-model:value="formData.other.attachments"
              :max-count="10"
              upload-text="上传附件"
              :file-size="50"
              :existing-files="formData.hgyAttachmentList"
              biz-type="attachments"
            />
          </a-form-item>
        </div>

        <div class="form-row">
          <a-form-item label="委托单上传" :name="['other', 'entrustDocument']" class="upload-item">
            <JUpload
              v-model:value="formData.other.entrustDocument"
              :max-count="5"
              upload-text="上传委托单"
              :file-size="50"
              :existing-files="formData.hgyAttachmentList"
              biz-type="entrustDocument"
            />
          </a-form-item>
        </div>

        <div class="form-row">
          <a-form-item label="特殊说明" :name="['other', 'specialNote']" class="full-width-item">
            <a-textarea v-model:value="formData.other.specialNote" placeholder="请输入特殊说明" :rows="4" :maxlength="500" show-count />
          </a-form-item>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { reactive, watch, computed, ref, inject } from 'vue';
  import { EnvironmentOutlined } from '@ant-design/icons-vue';
  import JAreaSelect from '@/components/Form/src/jeecg/components/JAreaSelect.vue';
  import JUpload from '@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import type { PassedReviewItem } from '@/api/manageCenter/appreciationPublish';

  // Props 定义
  interface Props {
    modelValue: any;
    locationLoading?: boolean;
    isEditMode?: boolean;
  }

  // Emits 定义
  interface Emits {
    (e: 'update:modelValue', value: any): void;
    (e: 'area-change', value: any): void;
    (e: 'get-current-location'): void;
    (e: 'service-type-change', value: number): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    locationLoading: false,
    isEditMode: false,
  });

  const emit = defineEmits<Emits>();

  // 注入委托单列表数据
  const entrustOrderList = inject<PassedReviewItem[]>('entrustOrderList', []);
  const entrustOrderLoading = inject<boolean>('entrustOrderLoading', false);

  // 表单数据
  const formData = reactive({ ...props.modelValue });

  // 监听表单数据变化
  watch(
    formData,
    (newVal) => {
      emit('update:modelValue', { ...newVal });
    },
    { deep: true }
  );

  // 监听 props 变化
  watch(
    () => props.modelValue,
    (newVal) => {
      Object.assign(formData, newVal);
    },
    { deep: true }
  );

  // 服务类型变化处理
  const handleServiceTypeChange = (value: number) => {
    emit('service-type-change', value);
  };

  // 委托单选择过滤
  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // 省市区变化处理
  const handleAreaChange = (value: any) => {
    emit('area-change', value);
  };

  // 获取当前位置
  const handleGetCurrentLocation = () => {
    emit('get-current-location');
  };

  // 表单验证规则
  const rules = computed(() => {
    const baseRules = {
      serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
      entrustOrderId: [{ required: true, message: '请选择关联委托单号', trigger: 'change' }],
      entrustInfo: {
        title: [{ required: true, message: '委托单位不能为空', trigger: 'blur' }],
        type: [{ required: true, message: '受委托单位不能为空', trigger: 'blur' }],
        noticeName: formData.serviceType === 3 ? [{ required: true, message: '请输入公告名称', trigger: 'blur' }] : [],
      },
      location: {
        province: [{ required: true, message: '请选择省份', trigger: 'change' }],
        city: [{ required: true, message: '请选择城市', trigger: 'change' }],
        area: [{ required: true, message: '请选择区域', trigger: 'change' }],
        detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
      },
    };

    // 资产处置特有验证规则
    if (formData.serviceType === 2) {
      baseRules['basicInfo'] = {
        assetName: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
        assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
        quantity: [{ required: true, message: '请输入资产数量', trigger: 'blur' }],
        unit: [{ required: true, message: '请输入计量单位', trigger: 'blur' }],
      };
    }

    return baseRules;
  });

  // 表单引用
  const formRef = ref();

  // 验证表单
  const validateForm = async (): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.ant-form-item-has-error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return false;
    }
  };

  // 暴露验证方法
  defineExpose({
    validateForm,
  });
</script>

<style lang="less" scoped>
  .step1 {
    .form-section {
      margin-bottom: 32px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 16px;
      padding-bottom: 8px;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: block;
        width: 4px;
        height: 18px;
        margin-right: 8px;
        background-color: #004c66;
      }
    }

    .form-row {
      &:last-child {
        border-bottom: none;
      }
    }

    // 服务类型选择样式
    .service-type-item {
      margin-bottom: 0;

      .service-type-select {
        width: 496px; /* 固定宽度496px */
      }
      /* 服务类型选择框文字颜色 */
      :deep(.ant-select-selection-item) {
        color: #004c66 !important;
      }

      .edit-mode-tip {
        margin-top: 4px;

        .tip-text {
          font-size: 12px;
          color: #999;
          line-height: 1.4;
        }
      }
    }

    // 委托单号选择样式
    .entrust-order-item {
      margin-bottom: 0;

      .entrust-order-select {
        width: 496px; /* 固定宽度496px */
      }
      /* 委托单号选择框文字颜色 */
      :deep(.ant-select-selection-item) {
        color: #004c66 !important;
      }
    }

    // 位置选择样式
    .location-row {
      display: flex;
      gap: 24px;
      align-items: flex-start;

      .location-cascader-item {
        flex: 1;
        min-width: 0;
        margin-bottom: 0;

        :deep(.area-select) {
          .ant-select {
            // 设置选择框文字居中
            .ant-select-selector {
              display: flex;
              align-items: center;

              .ant-select-selection-item {
                text-align: center;
                width: 100%;
                display: flex;
                align-items: center;
              }

              .ant-select-selection-placeholder {
                text-align: center;
                width: 100%;
                display: flex;
                align-items: center;
              }
            }
          }
        }
      }

      .location-address-item {
        flex: 1;
        min-width: 0;
        margin-bottom: 0;

        .address-input-wrapper {
          display: flex;
          gap: 12px;

          .address-input {
            flex: 1;
          }

          .location-btn {
            color: #1890ff;
            border: none;
            background: none;
            padding: 0;
            height: auto;
            line-height: 1;
          }
        }
      }
    }

    // 上传组件样式
    .upload-container {
      display: flex;
      align-items: flex-end;
      gap: 12px;
    }

    .upload-tip {
      font-size: 14px;
      color: #999;
      line-height: 1.4;
      align-self: flex-end;
      flex: 1;
    }

    .upload-item {
      width: 100%;
      margin-bottom: 0;

      .upload-container {
        .upload-tip {
          margin-top: 8px;
          color: #999;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }

    .upload-normal {
      cursor: pointer;
      flex-shrink: 0;

      :deep(.ant-upload-select) {
        width: 100px !important;
        height: 100px !important;
        background-color: #f2f2f2 !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;

        &::before,
        &::after {
          display: none !important;
        }

        .ant-upload {
          width: 100% !important;
          height: 100% !important;
          background-color: #f2f2f2 !important;
          border: none !important;
          border-radius: 4px !important;
          position: relative !important;
          overflow: hidden !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          &::before,
          &::after {
            display: none !important;
          }

          .anticon,
          span,
          .ant-upload-text,
          .ant-upload-hint,
          * {
            display: none !important;
          }

          &::after {
            content: '+' !important;
            width: 22px !important;
            height: 21px !important;
            font-size: 18px !important;
            color: #ddd !important;
            font-weight: 300 !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 10 !important;
            pointer-events: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: transparent !important;
            border: none !important;
            line-height: 1 !important;
          }
        }

        &:hover {
          background-color: #e8e8e8 !important;
          border-color: #bbb !important;

          .ant-upload {
            background-color: #e8e8e8 !important;

            &::after {
              color: #004c66 !important;
            }
          }
        }
      }
    }

    .upload-entrust {
      flex-shrink: 0;

      :deep(.ant-upload-select) {
        width: 178px !important;
        height: 100px !important;
        background-color: #f2f2f2 !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;

        &::before,
        &::after {
          display: none !important;
        }

        .ant-upload {
          width: 100% !important;
          height: 100% !important;
          background-color: #f2f2f2 !important;
          border: none !important;
          border-radius: 4px !important;
          position: relative !important;
          overflow: hidden !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          &::before,
          &::after {
            display: none !important;
          }

          .anticon,
          span,
          .ant-upload-text,
          .ant-upload-hint,
          * {
            display: none !important;
          }

          &::after {
            content: '+' !important;
            width: 22px !important;
            height: 21px !important;
            font-size: 18px !important;
            color: #ddd !important;
            font-weight: 300 !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 10 !important;
            pointer-events: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: transparent !important;
            border: none !important;
            line-height: 1 !important;
          }
        }

        &:hover {
          background-color: #e8e8e8 !important;
          border-color: #bbb !important;

          .ant-upload {
            background-color: #e8e8e8 !important;

            &::after {
              color: #004c66 !important;
            }
          }
        }
      }
    }

    // 基础表单行布局
    .basic-three-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .basic-three-item {
      flex: 1;
      min-width: 200px;
    }

    .basic-two-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .basic-two-item {
      flex: 1;
      min-width: 200px;
    }

    .entrust-two-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .entrust-two-item {
      flex: 1;
      min-width: 200px;
    }

    .full-width-item {
      width: 100%;
      margin-bottom: 0;
    }

    // 表单项样式调整
    :deep(.ant-form-item) {
      margin-bottom: 16px;
      align-items: flex-start;

      .ant-form-item-label {
        text-align: left;
        width: auto;
        min-width: 90px;
        padding-right: 0;
        display: flex;
        align-items: center;
        justify-content: end;
        height: 40px;

        label {
          color: #666;
          font-size: 16px;
          font-weight: 400;
          line-height: 1;

          &::after {
            content: '';
            margin: 0;
          }
        }
      }

      .ant-form-item-control {
        flex: 1;
        margin-left: 10px;
      }

      .ant-form-item-control-input {
        min-height: 40px;
      }

      .ant-select .ant-select-selector,
      .ant-picker {
        height: 40px !important;
        line-height: 40px !important;
      }

      .ant-input-number-input {
        height: 38px !important;
      }
    }

    // 输入框样式
    :deep(.ant-input),
    :deep(.ant-select-selector),
    :deep(.ant-picker),
    :deep(.ant-input-number),
    :deep(.ant-textarea) {
      border-radius: 6px;
    }

    // JUpload 组件样式调整
    :deep(.upload-normal) {
      .ant-upload-select-picture-card {
        width: 104px;
        height: 104px;
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    :deep(.upload-entrust) {
      .ant-upload-list {
        .ant-upload-list-item {
          margin-bottom: 8px;
        }
      }
    }

    // 编辑模式提示样式
    .edit-mode-tip {
      margin-top: 4px;

      .tip-text {
        font-size: 12px;
        color: #999;
        line-height: 1.4;
      }
    }
  }
</style>
