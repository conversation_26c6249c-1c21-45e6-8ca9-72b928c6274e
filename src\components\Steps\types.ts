// 步骤项接口定义
export interface StepItem {
  title: string; // 步骤标题
  description?: string; // 步骤描述（可选）
  icon?: string; // 步骤图标（可选）
  disabled?: boolean; // 是否禁用（可选）
}

// 步骤条组件Props接口
export interface CustomStepsProps {
  steps: StepItem[]; // 步骤数据数组
  current: number; // 当前激活的步骤索引
  size?: 'small' | 'default' | 'large'; // 尺寸（可选）
  direction?: 'horizontal' | 'vertical'; // 方向（可选）
}

// 步骤条组件事件接口
export interface CustomStepsEmits {
  (e: 'change', current: number): void; // 步骤改变事件
  (e: 'click', current: number, step: StepItem): void; // 步骤点击事件
}