# Audit 公共审核组件

## 概述

本目录包含了审核功能的公共组件，支持增值委托和自主委托的审核操作。这些组件从管理中心的待办功能中提取而来，可以在订单管理和管理中心等多个模块中复用。

## 组件结构

### 1. CommonAuditModal.vue

主审核弹窗组件，负责：

- 根据委托类型（增值/自主）显示不同的审核内容
- 统一的弹窗标题和布局管理
- 审核成功后的回调处理

### 2. CommonAppreciationAudit.vue

增值委托审核组件，支持：

- 基本信息展示
- 根据服务类型（竞价委托、资产处置、采购信息）显示不同详情
- 审核详情显示（已审核记录显示审核结果和审核意见）
- 审核操作（通过/拒绝）
- 审核意见填写

### 3. CommonSelfAudit.vue

自主委托审核组件，支持：

- 基本信息展示
- 根据服务类型显示不同详情
- 发布竞价标的时显示标的列表查看按钮
- 审核详情显示（已审核记录显示审核结果和审核意见）
- 审核操作（通过/拒绝）
- 审核意见填写

### 4. CommonAuctionItemsModal.vue

标的列表弹窗组件，用于：

- 显示自主委托中发布竞价标的的详细列表
- 支持多个标的信息的展示
- 标的类型、价格、位置等信息的格式化显示

## 新增功能：审核详情显示

### 功能说明

当获取到的数据中状态为3（通过）或4（拒绝）时，审核弹窗会自动显示审核详情区域，包括：

1. **审核结果显示**：使用不同颜色的标签显示"通过"或"拒绝"
2. **审核意见显示**：显示审核人员填写的审核意见（如果有）
3. **操作区域隐藏**：已审核的记录不再显示审核操作按钮

### 数据结构要求

审核详情信息需要在 `hgyEntrustOrder` 对象中包含以下字段：

```typescript
{
  hgyEntrustOrder: {
    status: number;        // 3-通过 4-拒绝
    auditOpinion?: string; // 审核意见（可选）
  }
}
```

### 显示逻辑

- **status === 3**：显示绿色"通过"标签
- **status === 4**：显示红色"拒绝"标签
- **auditOpinion存在**：显示审核意见内容
- **auditOpinion为空**：不显示审核意见行

## API接口

### 审核相关接口（src/api/manageCenter/audit.ts）

#### 增值委托审核

- `getAppreciationAuditDetail(id)` - 获取增值委托审核详情
- `submitAppreciationAudit(params)` - 提交增值委托审核结果

#### 自主委托审核

- `getSelfAuditDetail(id)` - 获取自主委托审核详情
- `submitSelfAudit(params)` - 提交自主委托审核结果

#### 标的列表

- `getAuctionItemsList(entrustOrderId)` - 获取委托单的标的列表

## 使用方式

### 基础使用

```vue
<template>
  <!-- 审核弹窗 -->
  <CommonAuditModal v-model:open="auditModalVisible" :record="currentAuditRecord" @close="handleCloseAuditModal" @success="handleAuditComplete" />
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { CommonAuditModal } from '/@/components/Audit';
  import type { AuditRecord } from '/@/components/Audit';

  const auditModalVisible = ref(false);
  const currentAuditRecord = ref<AuditRecord | null>(null);

  // 点击审核按钮
  function handleAudit(record: AuditRecord) {
    currentAuditRecord.value = record;
    auditModalVisible.value = true;
  }

  // 关闭审核弹窗
  function handleCloseAuditModal() {
    auditModalVisible.value = false;
    currentAuditRecord.value = null;
  }

  // 审核完成回调
  function handleAuditComplete() {
    handleCloseAuditModal();
    // 刷新列表或其他操作
    refreshList();
  }
</script>
```

### 单独使用标的列表弹窗

```vue
<template>
  <CommonAuctionItemsModal v-model:open="itemsModalVisible" :record-id="currentRecordId" @close="itemsModalVisible = false" />
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { CommonAuctionItemsModal } from '/@/components/Audit';

  const itemsModalVisible = ref(false);
  const currentRecordId = ref('');

  function showAuctionItems(recordId: string) {
    currentRecordId.value = recordId;
    itemsModalVisible.value = true;
  }
</script>
```

## 类型定义

```typescript
// 审核记录接口
export interface AuditRecord {
  id: string; // 委托单号
  entrustType: number; // 委托类型 1-增值 2-自主 3-供应
  serviceType: number; // 服务类型 1-竞价委托 2-资产处置 3-采购信息 4-供应 5-求购
  status: number; // 审核状态 2-待审 3-过审 4-拒审
  projectName: string; // 项目名称
  relationUser: string; // 联系人
  relationPhone: string; // 联系电话
  applicantUser: string; // 申请人
  auditUser: string; // 审核人
  submitTime: string; // 提交时间
  auditTime: string; // 审核时间
}

// 审核提交参数
export interface AuditSubmitParams {
  id: string;
  result: number; // 3-通过 4-拒绝
  remark?: string; // 审核意见
}

// 标的信息接口
export interface AuctionItem {
  id: string;
  auctionName: string;
  itemTitle: string;
  itemType: number;
  startPrice: number;
  appraisalPrice: number;
  reservePrice?: number;
  deposit: number;
  quantity: number;
  unit: string;
  province: string;
  city: string;
  district: string;
  address: string;
  description?: string;
  auctionMode: number;
  showCommission?: string;
  freeBidTime?: string;
  timedBidTime?: string;
}
```

## 功能特性

### 1. 委托类型区分

- **增值委托（entrustType: 1）**：显示增值委托相关信息
- **自主委托（entrustType: 2）**：显示自主委托相关信息，竞价委托类型支持查看标的列表
- **供求信息（entrustType: 3）**：暂未实现，显示开发中提示

### 2. 服务类型区分

- **竞价委托（serviceType: 1）**：自主委托时支持查看标的列表
- **资产处置（serviceType: 2）**：显示资产处置相关信息
- **采购信息（serviceType: 3）**：显示采购信息相关内容
- **供应/求购（serviceType: 4/5）**：供求信息相关（暂未实现）

### 3. 审核操作

- 支持通过/拒绝两种审核结果
- 可填写审核意见（可选）
- 表单验证确保审核结果必选
- 提交成功后自动刷新列表

### 4. 标的列表展示

- 仅在自主委托的发布竞价标的时显示
- 支持多个标的信息的详细展示
- 包含标的类型、价格、数量、位置等信息
- 支持标的类型的颜色标签区分

## 注意事项

1. **API地址**：使用现有的审核API接口，无需修改
2. **权限控制**：只有状态为"待审核"（status: 2）的记录才显示审核按钮
3. **错误处理**：API调用失败时会显示错误信息，并在适当情况下使用备用数据
4. **数据格式**：确保后端返回的数据格式与组件期望的接口一致

## 详情查看功能 ⭐ 新增

### DetailViewModal - 详情查看弹窗

专门用于已办和订单管理页面的详情查看功能，复用审核组件但隐藏审核操作区域。

#### 使用方法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <a-button @click="showDetail">查看详情</a-button>

    <!-- 详情查看弹窗 -->
    <DetailViewModal v-model:open="detailVisible" :record="selectedRecord" :entrust-type="1" :service-type="2" @close="handleClose" />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { DetailViewModal } from '/@/components/Audit';

  const detailVisible = ref(false);
  const selectedRecord = ref(null);

  function showDetail() {
    selectedRecord.value = {
      id: '123456',
      entrustType: 1,
      serviceType: 2,
      // ... 其他字段
    };
    detailVisible.value = true;
  }

  function handleClose() {
    detailVisible.value = false;
  }
</script>
```

#### 特性说明

1. **查看模式特性**
   - ✅ 隐藏审核操作：不显示审核表单和操作按钮
   - ✅ 优化布局：资产处置详情区域增加最小高度，提供更好的查看体验
   - ✅ 完整信息：显示所有详细信息，包括基本信息、详情内容、附件等
   - ✅ 附件操作：支持附件查看和下载功能

2. **适用场景**
   - 已办页面：查看已完成的审核记录详情
   - 订单管理：查看订单的详细信息
   - 历史记录：查看历史委托单详情
   - 报表查看：在报表中查看具体记录详情

## 扩展说明

如需添加新的委托类型或服务类型，需要：

1. 在对应的审核组件中添加新的条件分支
2. 更新API接口定义
3. 添加相应的详情展示逻辑
4. 更新类型映射和颜色配置
