<template>
  <div class="service-type-test">
    <h2>服务类型接口测试</h2>

    <div class="test-section">
      <h3>API接口测试</h3>
      <div class="test-buttons">
        <a-button type="primary" @click="testEntrustBiddingAPI"> 测试委托竞价API </a-button>
        <a-button type="primary" @click="testAutonomousBiddingAPI"> 测试自主竞价API </a-button>
      </div>
    </div>

    <div class="test-section">
      <h3>增值委托页面测试</h3>
      <div class="test-buttons">
        <a-button type="primary" @click="testAppreciationServiceType1"> 测试增值委托 - 服务类型1 </a-button>
        <a-button type="default" @click="testAppreciationServiceType2"> 测试增值委托 - 服务类型2 </a-button>
      </div>
    </div>

    <div class="test-section">
      <h3>自主委托页面测试</h3>
      <div class="test-buttons">
        <a-button type="primary" @click="testSelfServiceType1"> 测试自主委托 - 服务类型1 </a-button>
        <a-button type="default" @click="testSelfServiceType2"> 测试自主委托 - 服务类型2 </a-button>
      </div>
    </div>

    <div class="test-result">
      <h3>测试结果</h3>
      <pre>{{ testResult }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import { queryOrderItemTempById } from '@/api/orderManage/entrustBidding';
  import { queryOrderAuctionItemById } from '@/api/orderManage/autonomouslyBidding';

  const router = useRouter();
  const testResult = ref('');

  // 测试委托竞价API
  async function testEntrustBiddingAPI() {
    try {
      testResult.value = '正在测试委托竞价API...';
      const result = await queryOrderItemTempById('test-id-001');
      testResult.value = `委托竞价API测试结果:\n${JSON.stringify(result, null, 2)}`;

      // 检查数据结构
      if (result && result.hgyEntrustOrder && result.hgyAuctionItemTemp) {
        testResult.value += '\n\n✅ 数据结构正确：包含 hgyEntrustOrder 和 hgyAuctionItemTemp';
      } else {
        testResult.value += '\n\n❌ 数据结构异常：缺少必要字段';
      }

      message.success('委托竞价API测试完成');
    } catch (error) {
      console.error('委托竞价API测试失败:', error);
      testResult.value = `委托竞价API测试失败: ${error}`;
      message.error('委托竞价API测试失败');
    }
  }

  // 测试自主竞价API
  async function testAutonomousBiddingAPI() {
    try {
      testResult.value = '正在测试自主竞价API...';
      const result = await queryOrderAuctionItemById('test-id-002');
      testResult.value = `自主竞价API测试结果:\n${JSON.stringify(result, null, 2)}`;

      // 检查数据结构
      if (result && result.hgyEntrustOrder && result.hgyAuctionItemTemp) {
        testResult.value += '\n\n✅ 数据结构正确：包含 hgyEntrustOrder 和 hgyAuctionItemTemp';
        if (result.hgyAuction) {
          testResult.value += '\n✅ 包含 hgyAuction（自主委托特有）';
        }
      } else {
        testResult.value += '\n\n❌ 数据结构异常：缺少必要字段';
      }

      message.success('自主竞价API测试完成');
    } catch (error) {
      console.error('自主竞价API测试失败:', error);
      testResult.value = `自主竞价API测试失败: ${error}`;
      message.error('自主竞价API测试失败');
    }
  }

  // 测试增值委托页面 - 服务类型1
  function testAppreciationServiceType1() {
    testResult.value = '测试增值委托页面 - 服务类型1\n跳转到增值委托页面...';

    router.push({
      path: '/entrust/appreciationEntrust',
      query: {
        id: 'test-appreciation-001',
        serviceType: 1,
      },
    });

    message.info('跳转到增值委托页面 - 服务类型1');
  }

  // 测试增值委托页面 - 服务类型2
  function testAppreciationServiceType2() {
    testResult.value = '测试增值委托页面 - 服务类型2\n跳转到增值委托页面...';

    router.push({
      path: '/entrust/appreciationEntrust',
      query: {
        id: 'test-appreciation-002',
        serviceType: 2,
      },
    });

    message.info('跳转到增值委托页面 - 服务类型2');
  }

  // 测试自主委托页面 - 服务类型1
  function testSelfServiceType1() {
    testResult.value = '测试自主委托页面 - 服务类型1\n跳转到自主委托页面...';

    router.push({
      path: '/entrust/selfEntrust',
      query: {
        id: 'test-self-001',
        serviceType: 1,
      },
    });

    message.info('跳转到自主委托页面 - 服务类型1');
  }

  // 测试自主委托页面 - 服务类型2
  function testSelfServiceType2() {
    testResult.value = '测试自主委托页面 - 服务类型2\n跳转到自主委托页面...';

    router.push({
      path: '/entrust/selfEntrust',
      query: {
        id: 'test-self-002',
        serviceType: 2,
      },
    });

    message.info('跳转到自主委托页面 - 服务类型2');
  }
</script>

<style scoped>
  .service-type-test {
    padding: 20px;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
  }

  .test-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
  }

  .test-result {
    margin-top: 20px;
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 6px;
  }

  .test-result pre {
    white-space: pre-wrap;
    word-wrap: break-word;
  }
</style>
