<template>
  <div class="small-cards">
    <div class="cards-row">
      <!-- 左侧5个卡片 -->
      <div class="cards-column">
        <div v-for="(item, index) in leftCards" :key="index" class="small-card">
          <div class="card-icon">
            <div class="icon-bg">
              <img :src="item.icon" alt="" width="22" height="22" />
            </div>
          </div>
          <div class="card-content">
            <div class="card-label">{{ item.label }}</div>
            <div class="card-value">
              <CountTo :start="0" :end="item.value" :duration="2000" :decimals="0" />
              <span class="card-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧5个卡片 -->
      <div class="cards-column">
        <div v-for="(item, index) in rightCards" :key="index" class="small-card">
          <div class="card-icon">
            <div class="icon-bg">
              <img :src="item.icon" alt="" width="22" height="22" />
            </div>
          </div>
          <div class="card-content">
            <div class="card-label">{{ item.label }}</div>
            <div class="card-value">
              <CountTo :start="0" :end="item.value" :duration="2000" :decimals="item.decimals || 0" />
              <span class="card-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import CountTo from './CountTo.vue';
  import icon1 from '@/assets/images/visual/icon1.png';
  import icon2 from '@/assets/images/visual/icon2.png';
  import icon3 from '@/assets/images/visual/icon3.png';
  import icon4 from '@/assets/images/visual/icon4.png';
  import icon5 from '@/assets/images/visual/icon5.png';

  // 左侧卡片数据
  const leftCards = ref([
    { label: '标的总量', value: 5956, unit: '个', icon: icon1 },
    { label: '标的成交额', value: 35956, unit: '万', icon: icon2 },
    { label: '标的溢价率', value: 87.5, unit: '%', decimals: 2, icon: icon3 },
    { label: '成交量', value: 5956, unit: '个', icon: icon4 },
    { label: '标的溢价额', value: 5956, unit: '万', icon: icon5 },
  ]);

  // 右侧卡片数据
  const rightCards = ref([
    { label: '资产处置总量', value: 2956, unit: '个', icon: icon1 },
    { label: '资产处置成交额', value: 23689, unit: '万', icon: icon2 },
    { label: '资产处置溢价率', value: 95.23, unit: '%', decimals: 2, icon: icon3 },
    { label: '流拍量', value: 5956, unit: '个', icon: icon4 },
    { label: '资产处置溢价额', value: 5956, unit: '万', icon: icon5 },
  ]);
</script>

<style lang="less" scoped>
  .small-cards {
    height: 100%;
    padding: 20px;
  }

  .cards-row {
    display: flex;
    height: 100%;
    gap: 20px;
  }

  .cards-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .small-card {
    display: flex;
    align-items: center;
    width: 285px;
    height: 54px;
    padding: 3px 0;
    background: linear-gradient(90deg, rgba(19, 230, 219, 0.1) 0%, rgba(19, 230, 219, 0.5) 100%);

    border-radius: 10px;
    gap: 10px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(18, 230, 219, 0.3);
      border-color: rgba(18, 230, 219, 0.5);
    }
  }

  .card-icon {
    width: 48px;
    height: 48px;

    .icon-bg {
      width: 100%;
      height: 100%;
      background: url('@/assets/images/visual/icon-bg.png');
      background-size: cover;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 3px;
      padding-right: 1px;
    }
  }

  .card-content {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-label {
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #13e6db !important;
  }

  .card-value {
    display: flex;
    align-items: baseline;
    gap: 5px;
    margin-right: 14px;

    :deep(.count-to) {
      color: #fff;
      font-size: 22px;
      font-family: 'DIN Regular';
    }

    .card-unit {
      color: #ffffff;
      font-size: 12px;
    }
  }
</style>
