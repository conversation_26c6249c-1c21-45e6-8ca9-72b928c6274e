# 图片和附件回显功能修改说明

## 问题描述

### 1. 图片和附件回显问题

标的图片和附件在上传时是处理之后放入了 `hgyAttachmentList` 字段中，可根据该字段中的 `fileType` 判断是标的图片还是附件：

- `image` 是标的图片
- 其他类型都是附件

回显需要注意，使用的上传组件是 JUpload，在上传的时候返回的是 JSON 数据，里面包裹了 `fileType`、`fileSize`、`filePath` 和 `fileName`。

### 2. 联系人信息回显问题

联系人姓名和联系人电话的数据在 `hgyEntrustOrder` 中：

- `relationUser` 是联系人姓名
- `relationPhone` 是联系人电话
- `entrustCompanyId` 是委托方企业/处置企业 value
- `entrustCompanyName` 是委托方企业/处置企业 label
- `onEntrustCompanyId` 是受委托方企业 value
- `onEntrustCompanyName` 是受委托方企业 label

## 修改内容

### 1. 自主委托 Step1 组件修改

**文件：** `src/views/entrust/selfEntrust/components/Step1.vue`

#### 修改的内容：

1. **Props 接口扩展**：
   - 添加了 `hgyAttachmentList?: any[]` 字段用于接收附件数据
   - 修改了 `materials.images` 和 `materials.attachments` 的类型为 `any[] | string`，支持数组和字符串格式

2. **添加回显监听器**：

   ```typescript
   // 监听 hgyAttachmentList 变化，处理附件回显
   watch(
     () => formData.value.hgyAttachmentList,
     (newAttachmentList) => {
       if (newAttachmentList && Array.isArray(newAttachmentList) && newAttachmentList.length > 0) {
         console.log('Step1组件接收到附件列表，开始处理回显:', newAttachmentList);
         processAttachmentList(newAttachmentList);
       }
     },
     { immediate: true, deep: true }
   );
   ```

3. **添加回显处理函数**：

   ```typescript
   const processAttachmentList = (attachmentList: any[]) => {
     // 分离图片和附件
     const images: any[] = [];
     const attachments: any[] = [];

     attachmentList.forEach((item) => {
       const fileData = {
         fileName: item.fileName,
         filePath: item.filePath,
         fileSize: item.fileSize,
         fileType: item.fileType,
       };

       // 根据 fileType 判断是图片还是附件
       if (item.fileType === 'image') {
         images.push(fileData);
       } else {
         attachments.push(fileData);
       }
     });

     // 更新表单数据 - JUpload 组件期望接收 JSON 字符串格式
     if (images.length > 0) {
       const imagesJson = JSON.stringify(images);
       formData.value.materials.images = imagesJson;
       formData.value.other.images = imagesJson;
     }

     if (attachments.length > 0) {
       const attachmentsJson = JSON.stringify(attachments);
       formData.value.materials.attachments = attachmentsJson;
       formData.value.other.attachments = attachmentsJson;
     }
   };
   ```

### 2. 自主委托主页面修改

**文件：** `src/views/entrust/selfEntrust/index.vue`

#### 修改的内容：

1. **stepOneData 数据结构扩展**：
   - 添加了 `hgyAttachmentList: [] as any[]` 字段
   - 修改了 `materials.images` 和 `materials.attachments` 的类型定义

2. **fetchEntrustDetail 函数修改**：

   ```typescript
   // 回显委托信息
   if (hgyEntrustOrder) {
     stepOneData.entrustInfo.title = hgyEntrustOrder.entrustCompanyName || '';
     stepOneData.entrustInfo.type = hgyEntrustOrder.onEntrustCompanyName || '';
     stepOneData.basicInfo.entrustCompanyId = hgyEntrustOrder.entrustCompanyId?.toString() || '';

     // 回显联系人信息
     stepThreeData.contact.contactName = hgyEntrustOrder.relationUser || '';
     stepThreeData.contact.contactPhone = hgyEntrustOrder.relationPhone || '';
   }

   // 回显附件信息
   if (hgyAttachmentList && Array.isArray(hgyAttachmentList) && hgyAttachmentList.length > 0) {
     console.log('开始处理附件回显:', hgyAttachmentList);
     stepOneData.hgyAttachmentList = hgyAttachmentList;
   }
   ```

3. **fetchCompanyList 函数修改**：
   ```typescript
   // 自动填充委托单位和受委托单位名称（仅在非编辑模式下）
   if (!isEditMode.value) {
     const entrustCompany = result.find((item) => item.value === '1004');
     const entrusteeCompany = result.find((item) => item.value === '1003');
     // ... 填充逻辑
   }
   ```

### 3. 增值委托 Step1 组件修改

**文件：** `src/views/entrust/appreciationEntrust/components/Step1.vue`

#### 修改的内容：

1. **Props 接口扩展**：
   - 添加了 `hgyAttachmentList?: any[]` 字段

2. **添加回显监听器和处理函数**：
   - 与自主委托类似的回显逻辑
   - 适配增值委托的数据结构（materials 字段已经是 string 类型）

### 4. 增值委托主页面修改

**文件：** `src/views/entrust/appreciationEntrust/index.vue`

#### 修改的内容：

1. **stepOneData 数据结构扩展**：
   - 添加了 `hgyAttachmentList: [] as any[]` 字段

2. **fetchEntrustDetail 函数修改**：

   ```typescript
   // 回显委托信息
   if (hgyEntrustOrder) {
     stepOneData.entrustInfo.title = hgyEntrustOrder.entrustCompanyName || '';
     stepOneData.entrustInfo.type = hgyEntrustOrder.onEntrustCompanyName || '';

     // 回显联系人信息
     stepTwoData.contactInfo.contactName = hgyEntrustOrder.relationUser || '';
     stepTwoData.contactInfo.contactPhone = hgyEntrustOrder.relationPhone || '';
   }

   // 回显附件信息
   if (hgyAttachmentList && Array.isArray(hgyAttachmentList) && hgyAttachmentList.length > 0) {
     stepOneData.hgyAttachmentList = hgyAttachmentList;
   }
   ```

3. **fetchCompanyList 函数修改**：
   - 添加了编辑模式判断，避免覆盖从后端获取的企业信息

## 工作原理

### 1. 附件回显数据流向

```
后端 hgyAttachmentList → 主页面 stepOneData.hgyAttachmentList → Step1组件 Props → 监听器触发 → 处理函数分离数据 → 更新 JUpload 组件
```

### 2. 联系人信息回显数据流向

```
后端 hgyEntrustOrder → fetchEntrustDetail 函数 → stepThreeData.contact (自主委托) / stepTwoData.contactInfo (增值委托)
```

### 3. 数据格式转换

- **附件数据**：
  - 后端返回的 `hgyAttachmentList` 是对象数组
  - 根据 `fileType` 字段分离图片和附件
  - 转换为 JUpload 组件期望的 JSON 字符串格式
  - 设置到对应的表单字段中

- **联系人数据**：
  - 直接从 `hgyEntrustOrder.relationUser` 和 `hgyEntrustOrder.relationPhone` 获取
  - 设置到对应的联系人表单字段中

### 4. 企业信息处理

- **新建模式**：使用硬编码的企业ID自动填充
- **编辑模式**：使用从后端返回的实际企业信息，避免覆盖

### 5. JUpload 组件回显

- JUpload 组件的 `parseArrayValue` 函数会解析 JSON 字符串
- 根据 `fileName` 和 `filePath` 创建文件列表
- 显示已上传的文件

## 测试方法

### 1. 编辑模式测试

#### 附件回显测试：

- 进入自主委托或增值委托的编辑页面
- 查看控制台日志，确认附件数据被正确处理
- 验证图片和附件是否正确回显在对应的上传组件中

#### 联系人信息回显测试：

- 进入编辑页面，检查联系人姓名和电话是否正确回显
- 自主委托：检查第三步的联系人信息
- 增值委托：检查第二步的联系人信息

#### 企业信息回显测试：

- 验证委托方企业和受委托方企业名称是否正确显示
- 确认编辑模式下不会被硬编码值覆盖

### 2. 数据验证

#### 附件数据验证：

- 检查 `hgyAttachmentList` 数据结构是否正确
- 验证 `fileType` 字段的值（'image' 或其他）
- 确认文件路径、文件名、文件大小等信息完整

#### 联系人数据验证：

- 检查 `hgyEntrustOrder.relationUser` 和 `hgyEntrustOrder.relationPhone` 字段
- 验证数据是否正确传递到表单组件

## 注意事项

### 1. 数据格式

- **附件数据**：JUpload 组件期望接收 JSON 字符串格式的数据
- 数据结构必须包含 `fileName`、`filePath`、`fileSize` 字段
- **联系人数据**：直接字符串格式，无需特殊处理

### 2. 类型安全

- 修改了相关的 TypeScript 类型定义
- 确保类型兼容性
- 支持数组和字符串两种格式的附件数据

### 3. 向后兼容

- 保持了原有的数据结构和接口
- 只是添加了新的回显功能
- 不影响新建模式的正常使用

### 4. 编辑模式处理

- 区分新建模式和编辑模式
- 编辑模式下使用后端返回的实际数据
- 避免硬编码值覆盖真实数据

### 5. 调试信息

- 添加了详细的控制台日志
- 便于排查回显问题
- 可以通过日志确认数据流向

## 相关文件

- `src/views/entrust/selfEntrust/components/Step1.vue`
- `src/views/entrust/selfEntrust/index.vue`
- `src/views/entrust/appreciationEntrust/components/Step1.vue`
- `src/views/entrust/appreciationEntrust/index.vue`
- `src/components/Form/src/jeecg/components/JUpload/JUpload.vue`
