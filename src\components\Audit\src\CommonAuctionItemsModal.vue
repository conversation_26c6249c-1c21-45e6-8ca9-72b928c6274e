<template>
  <CustomModal
    v-model:open="visible"
    title="标的列表"
    :width="1000"
    :show-footer="false"
    @close="handleClose"
  >
    <div class="auction-items-modal">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" tip="加载标的列表..." />
      </div>
      
      <!-- 标的列表 -->
      <div v-else class="items-container">
        <div v-if="auctionItems.length === 0" class="empty-state">
          <a-empty description="暂无标的信息" />
        </div>
        
        <div v-else class="items-list">
          <div
            v-for="(item, index) in auctionItems"
            :key="index"
            class="item-card"
          >
            <div class="item-header">
              <h4 class="item-title">标的{{ index + 1 }}</h4>
              <a-tag :color="getItemTypeColor(item.itemType)">
                {{ getItemTypeText(item.itemType) }}
              </a-tag>
            </div>
            
            <div class="item-content">
              <div class="item-row">
                <div class="item-field">
                  <span class="field-label">关联拍卖会：</span>
                  <span class="field-value">{{ item.auctionName || '-' }}</span>
                </div>
                <div class="item-field">
                  <span class="field-label">标的标题：</span>
                  <span class="field-value">{{ item.itemTitle || '-' }}</span>
                </div>
              </div>
              
              <div class="item-row">
                <div class="item-field">
                  <span class="field-label">起拍价：</span>
                  <span class="field-value price">{{ formatPrice(item.startPrice) }}</span>
                </div>
                <div class="item-field">
                  <span class="field-label">评估价格：</span>
                  <span class="field-value price">{{ formatPrice(item.appraisalPrice) }}</span>
                </div>
              </div>
              
              <div class="item-row">
                <div class="item-field">
                  <span class="field-label">保证金：</span>
                  <span class="field-value price">{{ formatPrice(item.deposit) }}</span>
                </div>
                <div class="item-field">
                  <span class="field-label">标的数量：</span>
                  <span class="field-value">{{ item.quantity }}{{ item.unit || '' }}</span>
                </div>
              </div>
              
              <div class="item-row">
                <div class="item-field full-width">
                  <span class="field-label">存放位置：</span>
                  <span class="field-value">
                    {{ getFullAddress(item) }}
                  </span>
                </div>
              </div>
              
              <div v-if="item.description" class="item-row">
                <div class="item-field full-width">
                  <span class="field-label">标的描述：</span>
                  <span class="field-value">{{ item.description }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="modal-footer">
        <a-button type="primary" @click="handleClose">关闭</a-button>
      </div>
    </div>
  </CustomModal>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { CustomModal } from '/@/components/Modal';
  import { getAuctionItemsList } from '/@/api/manageCenter/audit';
  import type { AuctionItem } from '../types';

  interface Props {
    open: boolean;
    recordId: string | undefined;
  }

  interface Emits {
    (e: 'update:open', value: boolean): void;
    (e: 'close'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const visible = ref(props.open);
  const loading = ref(false);
  const auctionItems = ref<AuctionItem[]>([]);

  // 监听外部传入的open状态
  watch(
    () => props.open,
    (newValue) => {
      visible.value = newValue;
      if (newValue) {
        fetchAuctionItems();
      }
    }
  );

  // 监听内部visible状态变化
  watch(visible, (newValue) => {
    emit('update:open', newValue);
  });

  // 获取标的列表数据
  async function fetchAuctionItems() {
    if (!props.recordId) return;
    
    loading.value = true;
    try {
      // 调用实际的API获取标的列表
      const response = await getAuctionItemsList(props.recordId);
      auctionItems.value = response || [];
    } catch (error) {
      console.error('获取标的列表失败:', error);
      message.error('获取标的列表失败');
      // 如果API调用失败，显示模拟数据作为备用
      auctionItems.value = [
        {
          id: '1',
          auctionName: '2024年第一期拍卖会',
          itemTitle: '办公设备一批',
          itemType: 1,
          startPrice: 50000,
          appraisalPrice: 80000,
          deposit: 10000,
          quantity: 1,
          unit: '批',
          province: '广东省',
          city: '深圳市',
          district: '南山区',
          address: '科技园南区某大厦',
          description: '包含电脑、打印机、办公桌椅等办公设备',
          auctionMode: 1,
        },
        {
          id: '2',
          auctionName: '2024年第一期拍卖会',
          itemTitle: '机动车辆',
          itemType: 2,
          startPrice: 120000,
          appraisalPrice: 150000,
          deposit: 20000,
          quantity: 1,
          unit: '辆',
          province: '广东省',
          city: '深圳市',
          district: '福田区',
          address: '某停车场',
          description: '2020年购买的商务车辆，车况良好',
          auctionMode: 1,
        },
      ];
    } finally {
      loading.value = false;
    }
  }

  // 获取标的类型文本
  function getItemTypeText(type: number) {
    const typeMap: Record<number, string> = {
      1: '物资/设备',
      2: '机动车',
      3: '房产',
      4: '土地',
      5: '其他',
    };
    return typeMap[type] || '未知';
  }

  // 获取标的类型颜色
  function getItemTypeColor(type: number) {
    const colorMap: Record<number, string> = {
      1: 'blue',
      2: 'green',
      3: 'orange',
      4: 'purple',
      5: 'default',
    };
    return colorMap[type] || 'default';
  }

  // 格式化价格
  function formatPrice(price: number | undefined) {
    if (price === undefined || price === null) return '-';
    return `¥${price.toLocaleString()}`;
  }

  // 获取完整地址
  function getFullAddress(item: AuctionItem) {
    const parts = [item.province, item.city, item.district, item.address].filter(Boolean);
    return parts.join('') || '-';
  }

  // 关闭弹窗
  function handleClose() {
    visible.value = false;
    emit('close');
  }
</script>

<style lang="less" scoped>
  .auction-items-modal {
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
    }

    .items-container {
      max-height: 600px;
      overflow-y: auto;
      
      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
      }
      
      .items-list {
        .item-card {
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          margin-bottom: 16px;
          overflow: hidden;
          
          .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #fafafa;
            border-bottom: 1px solid #e8e8e8;
            
            .item-title {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              color: #333;
            }
          }
          
          .item-content {
            padding: 16px;
            
            .item-row {
              display: flex;
              margin-bottom: 12px;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              .item-field {
                flex: 1;
                display: flex;
                
                &.full-width {
                  flex: none;
                  width: 100%;
                }
                
                .field-label {
                  font-weight: 500;
                  color: #666;
                  min-width: 80px;
                  margin-right: 8px;
                }
                
                .field-value {
                  color: #333;
                  flex: 1;
                  
                  &.price {
                    color: #f5222d;
                    font-weight: 600;
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .modal-footer {
      display: flex;
      justify-content: center;
      padding-top: 20px;
      border-top: 1px solid #e8e8e8;
      margin-top: 20px;
    }
  }
</style>
