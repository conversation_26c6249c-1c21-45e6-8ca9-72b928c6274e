<template>
  <div class="audit-test">
    <h2>审核功能测试</h2>

    <div class="test-section">
      <h3>API接口测试</h3>
      <div class="test-buttons">
        <a-button type="primary" @click="testAppreciationBiddingAPI"> 测试增值委托竞价审核API </a-button>
        <a-button type="primary" @click="testSelfBiddingAPI"> 测试自主委托竞价审核API </a-button>
        <a-button type="default" @click="testAssetAPI"> 测试资产处置审核API（通用） </a-button>
        <a-button type="default" @click="testProcurementAPI"> 测试采购审核API（通用） </a-button>
        <a-button type="dashed" @click="testUnifiedAPI"> 测试统一API调用逻辑 </a-button>
      </div>
    </div>

    <div class="test-section">
      <h3>审核弹窗测试</h3>
      <div class="test-buttons">
        <a-button type="primary" @click="testAppreciationBiddingAudit"> 增值委托竞价审核 </a-button>
        <a-button type="primary" @click="testSelfBiddingAudit"> 自主委托竞价审核 </a-button>
        <a-button type="default" @click="testAppreciationAssetAudit"> 增值委托资产处置审核 </a-button>
        <a-button type="default" @click="testSelfAssetAudit"> 自主委托资产处置审核 </a-button>
        <a-button type="default" @click="testAppreciationProcurementAudit"> 增值委托采购审核 </a-button>
        <a-button type="default" @click="testSelfProcurementAudit"> 自主委托采购审核 </a-button>
      </div>
    </div>

    <div class="test-result">
      <h3>测试结果</h3>
      <pre>{{ testResult }}</pre>
    </div>

    <!-- 审核弹窗 -->
    <CommonAuditModal v-model:open="auditModalVisible" :record="currentAuditRecord" @close="handleCloseAuditModal" @success="handleAuditComplete" />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  import { CommonAuditModal } from '/@/components/Audit';
  import type { AuditRecord } from '/@/components/Audit';
  import {
    getAuditDetailByType,
    getAppreciationBiddingAuditDetail,
    getSelfBiddingAuditDetail,
    getAppreciationAuditDetail,
    getSelfAuditDetail,
    getProcurementAuditDetail,
  } from '/@/api/manageCenter/audit';

  const testResult = ref('');
  const auditModalVisible = ref(false);
  const currentAuditRecord = ref<AuditRecord | null>(null);

  // 测试增值委托竞价审核API
  async function testAppreciationBiddingAPI() {
    try {
      testResult.value = '正在测试增值委托竞价审核API...';
      const result = await getAppreciationBiddingAuditDetail('test-appreciation-bidding-001');
      testResult.value = `增值委托竞价审核API测试结果:\n${JSON.stringify(result, null, 2)}`;

      if (result && result.hgyEntrustOrder && result.hgyAuctionItemTemp) {
        testResult.value += '\n\n✅ 数据结构正确：包含 hgyEntrustOrder 和 hgyAuctionItemTemp';
      } else {
        testResult.value += '\n\n❌ 数据结构异常：缺少必要字段';
      }

      message.success('增值委托竞价审核API测试完成');
    } catch (error) {
      console.error('增值委托竞价审核API测试失败:', error);
      testResult.value = `增值委托竞价审核API测试失败: ${error}`;
      message.error('增值委托竞价审核API测试失败');
    }
  }

  // 测试自主委托竞价审核API
  async function testSelfBiddingAPI() {
    try {
      testResult.value = '正在测试自主委托竞价审核API...';
      const result = await getSelfBiddingAuditDetail('test-self-bidding-001');
      testResult.value = `自主委托竞价审核API测试结果:\n${JSON.stringify(result, null, 2)}`;

      if (result && result.hgyEntrustOrder && result.hgyAuctionItemTemp) {
        testResult.value += '\n\n✅ 数据结构正确：包含 hgyEntrustOrder 和 hgyAuctionItemTemp';
        if (result.hgyAuction) {
          testResult.value += '\n✅ 包含 hgyAuction（自主委托特有）';
        }
      } else {
        testResult.value += '\n\n❌ 数据结构异常：缺少必要字段';
      }

      message.success('自主委托竞价审核API测试完成');
    } catch (error) {
      console.error('自主委托竞价审核API测试失败:', error);
      testResult.value = `自主委托竞价审核API测试失败: ${error}`;
      message.error('自主委托竞价审核API测试失败');
    }
  }

  // 测试资产处置审核API（通用）
  async function testAssetAPI() {
    try {
      testResult.value = '正在测试资产处置审核API（通用）...';
      const result = await getAppreciationAuditDetail('test-asset-001');
      testResult.value = `资产处置审核API测试结果:\n${JSON.stringify(result, null, 2)}`;
      testResult.value += '\n\n📝 说明：资产处置审核不论是增值委托还是自主委托，都使用同一个接口';
      message.success('资产处置审核API测试完成');
    } catch (error) {
      console.error('资产处置审核API测试失败:', error);
      testResult.value = `资产处置审核API测试失败: ${error}`;
      message.error('资产处置审核API测试失败');
    }
  }

  // 测试采购审核API（通用）
  async function testProcurementAPI() {
    try {
      testResult.value = '正在测试采购审核API（通用）...';
      const result = await getProcurementAuditDetail('test-procurement-001');
      testResult.value = `采购审核API测试结果:\n${JSON.stringify(result, null, 2)}`;
      testResult.value += '\n\n📝 说明：采购审核不论是增值委托还是自主委托，都使用同一个接口';

      if (result && result.hgyEntrustOrder && result.hgyProcurement) {
        testResult.value += '\n\n✅ 数据结构正确：包含 hgyEntrustOrder 和 hgyProcurement';
      } else {
        testResult.value += '\n\n❌ 数据结构异常：缺少必要字段';
      }

      message.success('采购审核API测试完成');
    } catch (error) {
      console.error('采购审核API测试失败:', error);
      testResult.value = `采购审核API测试失败: ${error}`;
      message.error('采购审核API测试失败');
    }
  }

  // 测试统一API调用逻辑
  async function testUnifiedAPI() {
    try {
      testResult.value = '正在测试统一API调用逻辑...\n\n';

      // 测试增值委托竞价
      testResult.value += '1. 测试增值委托竞价（entrustType=1, serviceType=1）\n';
      const result1 = await getAuditDetailByType('test-001', 1, 1);
      testResult.value += `   结果：调用了增值委托竞价API\n\n`;

      // 测试自主委托竞价
      testResult.value += '2. 测试自主委托竞价（entrustType=2, serviceType=1）\n';
      const result2 = await getAuditDetailByType('test-002', 2, 1);
      testResult.value += `   结果：调用了自主委托竞价API\n\n`;

      // 测试增值委托资产处置
      testResult.value += '3. 测试增值委托资产处置（entrustType=1, serviceType=2）\n';
      const result3 = await getAuditDetailByType('test-003', 1, 2);
      testResult.value += `   结果：调用了资产处置通用API\n\n`;

      // 测试自主委托资产处置
      testResult.value += '4. 测试自主委托资产处置（entrustType=2, serviceType=2）\n';
      const result4 = await getAuditDetailByType('test-004', 2, 2);
      testResult.value += `   结果：调用了资产处置通用API\n\n`;

      // 测试增值委托采购
      testResult.value += '5. 测试增值委托采购（entrustType=1, serviceType=3）\n';
      const result5 = await getAuditDetailByType('test-005', 1, 3);
      testResult.value += `   结果：调用了采购通用API\n\n`;

      // 测试自主委托采购
      testResult.value += '6. 测试自主委托采购（entrustType=2, serviceType=3）\n';
      const result6 = await getAuditDetailByType('test-006', 2, 3);
      testResult.value += `   结果：调用了采购通用API\n\n`;

      testResult.value += '✅ 统一API调用逻辑测试完成！\n';
      testResult.value += '📝 总结：\n';
      testResult.value += '   - 竞价委托：根据委托类型调用不同API\n';
      testResult.value += '   - 资产处置：不论委托类型，都使用同一个API\n';
      testResult.value += '   - 采购信息：不论委托类型，都使用同一个API';

      message.success('统一API调用逻辑测试完成');
    } catch (error) {
      console.error('统一API调用逻辑测试失败:', error);
      testResult.value += `\n❌ 测试失败: ${error}`;
      message.error('统一API调用逻辑测试失败');
    }
  }

  // 测试增值委托竞价审核弹窗
  function testAppreciationBiddingAudit() {
    currentAuditRecord.value = {
      id: 'test-appreciation-bidding-001',
      entrustType: 1, // 增值委托
      serviceType: 1, // 竞价委托
      projectName: '测试增值委托竞价项目',
      relationUser: '张三',
      relationPhone: '13800138000',
      applicantUser: '李四',
      submitTime: '2024-01-15 10:30:00',
      status: 2, // 待审核
    };
    auditModalVisible.value = true;
    testResult.value = '打开增值委托竞价审核弹窗';
  }

  // 测试自主委托竞价审核弹窗
  function testSelfBiddingAudit() {
    currentAuditRecord.value = {
      id: 'test-self-bidding-001',
      entrustType: 2, // 自主委托
      serviceType: 1, // 竞价委托
      projectName: '测试自主委托竞价项目',
      relationUser: '王五',
      relationPhone: '13900139000',
      applicantUser: '赵六',
      submitTime: '2024-01-15 14:30:00',
      status: 2, // 待审核
    };
    auditModalVisible.value = true;
    testResult.value = '打开自主委托竞价审核弹窗';
  }

  // 测试增值委托资产处置审核弹窗
  function testAppreciationAssetAudit() {
    currentAuditRecord.value = {
      id: 'test-appreciation-asset-001',
      entrustType: 1, // 增值委托
      serviceType: 2, // 资产处置
      projectName: '测试增值委托资产处置项目',
      relationUser: '孙七',
      relationPhone: '13700137000',
      applicantUser: '周八',
      submitTime: '2024-01-15 16:30:00',
      status: 2, // 待审核
    };
    auditModalVisible.value = true;
    testResult.value = '打开增值委托资产处置审核弹窗';
  }

  // 测试自主委托资产处置审核弹窗
  function testSelfAssetAudit() {
    currentAuditRecord.value = {
      id: 'test-self-asset-001',
      entrustType: 2, // 自主委托
      serviceType: 2, // 资产处置
      projectName: '测试自主委托资产处置项目',
      relationUser: '吴九',
      relationPhone: '13600136000',
      applicantUser: '郑十',
      submitTime: '2024-01-15 18:30:00',
      status: 2, // 待审核
    };
    auditModalVisible.value = true;
    testResult.value = '打开自主委托资产处置审核弹窗';
  }

  // 测试增值委托采购审核弹窗
  function testAppreciationProcurementAudit() {
    currentAuditRecord.value = {
      id: 'test-appreciation-procurement-001',
      entrustType: 1, // 增值委托
      serviceType: 3, // 采购信息
      projectName: '测试增值委托采购项目',
      relationUser: '陈十一',
      relationPhone: '13500135000',
      applicantUser: '刘十二',
      submitTime: '2024-01-15 20:30:00',
      status: 2, // 待审核
    };
    auditModalVisible.value = true;
    testResult.value = '打开增值委托采购审核弹窗';
  }

  // 测试自主委托采购审核弹窗
  function testSelfProcurementAudit() {
    currentAuditRecord.value = {
      id: 'test-self-procurement-001',
      entrustType: 2, // 自主委托
      serviceType: 3, // 采购信息
      projectName: '测试自主委托采购项目',
      relationUser: '黄十三',
      relationPhone: '13400134000',
      applicantUser: '杨十四',
      submitTime: '2024-01-15 22:30:00',
      status: 2, // 待审核
    };
    auditModalVisible.value = true;
    testResult.value = '打开自主委托采购审核弹窗';
  }

  // 关闭审核弹窗
  function handleCloseAuditModal() {
    auditModalVisible.value = false;
    currentAuditRecord.value = null;
  }

  // 审核完成后的回调
  function handleAuditComplete() {
    handleCloseAuditModal();
    testResult.value += '\n\n✅ 审核操作完成';
    message.success('审核操作完成');
  }
</script>

<style scoped>
  .audit-test {
    padding: 20px;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
  }

  .test-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 10px;
  }

  .test-result {
    margin-top: 20px;
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 6px;
  }

  .test-result pre {
    white-space: pre-wrap;
    word-wrap: break-word;
  }
</style>
