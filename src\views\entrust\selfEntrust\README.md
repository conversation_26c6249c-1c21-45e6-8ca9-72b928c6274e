# 自助委托页面 - 自主委托确认弹窗功能

## 功能概述

在自助委托页面进入时，会自动弹出一个确认弹窗，用户需要确认是否选择自主服务。

## 功能特性

### ✅ 自动弹出确认弹窗
- 页面加载时自动显示确认弹窗
- 弹窗标题：**自主委托**
- 弹窗宽度：600px
- 不能通过点击遮罩关闭（`mask-closable="false"`）

### ✅ 弹窗内容
```
我方仅将客户资产信息在我网进行公示，我司不会对客户所发布信息做任何协助
请确认是否选择自主服务
```

### ✅ 按钮配置
- **放弃按钮**：点击后跳转回增值委托页面
- **确认按钮**：点击后关闭弹窗，显示成功消息，继续自助委托流程

### ✅ 样式设计
- 使用自定义的 `CustomModal` 组件
- 头部渐变背景：`rgba(0, 76, 102, 0.9)` → `rgba(0, 76, 102, 0.6)`
- 左侧logo + 中间标题 + 右侧关闭按钮的布局
- 内容区域居中显示，字体大小16px，行高1.6

## 技术实现

### 1. 组件导入
```typescript
import { CustomModal } from '/@/components/Modal'
```

### 2. 状态管理
```typescript
// 自主委托确认弹窗状态 - 默认为true，页面进入时自动显示
const confirmModalVisible = ref(true);
```

### 3. 事件处理
```typescript
// 确认自主委托
const handleConfirmSelfEntrust = () => {
  confirmModalVisible.value = false;
  message.success('已选择自主服务');
};

// 放弃自主委托，跳转到增值委托页面
const handleAbandonSelfEntrust = () => {
  confirmModalVisible.value = false;
  router.push('/entrust/appreciationEntrust');
};
```

### 4. 模板使用
```vue
<CustomModal
  v-model:open="confirmModalVisible"
  title="自主委托"
  width="600"
  cancel-text="放弃"
  confirm-text="确认"
  :mask-closable="false"
  @confirm="handleConfirmSelfEntrust"
  @cancel="handleAbandonSelfEntrust"
>
  <div class="confirm-modal-content">
    <p>我方仅将客户资产信息在我网进行公示，我司不会对客户所发布信息做任何协助</p>
    <p>请确认是否选择自主服务</p>
  </div>
</CustomModal>
```

## 用户交互流程

### 场景1：用户确认选择自主服务
1. 用户进入自助委托页面
2. 自动弹出确认弹窗
3. 用户点击"确认"按钮
4. 弹窗关闭，显示"已选择自主服务"消息
5. 用户继续自助委托流程

### 场景2：用户放弃自主服务
1. 用户进入自助委托页面
2. 自动弹出确认弹窗
3. 用户点击"放弃"按钮
4. 弹窗关闭，自动跳转到增值委托页面

### 场景3：用户点击右上角关闭按钮
1. 用户进入自助委托页面
2. 自动弹出确认弹窗
3. 用户点击右上角关闭按钮
4. 弹窗关闭（与点击"放弃"按钮效果相同）

## 样式说明

### 弹窗头部
- 高度：48px
- 背景：线性渐变 `linear-gradient(180deg, rgba(0, 76, 102, 0.9) 0%, rgba(0, 76, 102, 0.6) 100%)`
- 左侧：项目logo图标
- 中间：标题文字（18px，白色，PingFang Bold字体）
- 右侧：关闭图标

### 弹窗内容
- 内边距：20px 0
- 文字居中对齐
- 字体大小：16px
- 行高：1.6
- 颜色：#333

### 按钮样式
- 放弃按钮：白色背景，灰色边框和文字
- 确认按钮：主题色背景（rgba(0, 76, 102, 0.9)）

## 注意事项

1. **路由依赖**：确保增值委托页面的路由路径 `/entrust/appreciationEntrust` 正确配置
2. **图标资源**：需要确保项目中存在 `model-logo.svg` 和 `close.svg` 图标文件
3. **字体支持**：需要确保项目中已引入 PingFang Bold 字体
4. **弹窗阻塞**：弹窗设置为不能通过点击遮罩关闭，用户必须做出选择
5. **状态管理**：弹窗状态初始值为 `true`，确保页面进入时自动显示

## 测试建议

### 功能测试
- [ ] 页面进入时弹窗自动显示
- [ ] 点击"确认"按钮弹窗关闭并显示成功消息
- [ ] 点击"放弃"按钮跳转到增值委托页面
- [ ] 点击右上角关闭按钮的行为
- [ ] 点击遮罩无法关闭弹窗

### 样式测试
- [ ] 弹窗头部渐变背景显示正确
- [ ] logo和关闭图标显示正确
- [ ] 标题字体和颜色正确
- [ ] 内容区域样式正确
- [ ] 按钮样式和交互效果正确

### 兼容性测试
- [ ] 不同浏览器下的显示效果
- [ ] 不同屏幕尺寸下的响应式表现
- [ ] 移动端适配效果

## 相关文件

- **主页面**：`src/views/entrust/selfEntrust/index.vue`
- **弹窗组件**：`src/components/Modal/src/CustomModal.vue`
- **测试页面**：`src/views/demo/modal/custom-modal/entrust-test.vue`
- **组件文档**：`src/components/Modal/README.md`
