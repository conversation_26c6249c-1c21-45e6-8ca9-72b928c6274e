/**
 * 审核记录接口
 */
export interface AuditRecord {
  id: string; // 委托单号
  entrustType: number; // 委托类型 1-增值 2-自主 3-供应
  serviceType: number; // 服务类型 1-竞价委托 2-资产处置 3-采购信息 4-供应 5-求购
  status: number; // 审核状态 2-待审 3-过审 4-拒审
  projectName: string; // 项目名称
  relationUser: string; // 联系人
  relationPhone: string; // 联系电话
  applicantUser: string; // 申请人
  auditUser: string; // 审核人
  submitTime: string; // 提交时间
  auditTime: string; // 审核时间
}

/**
 * 审核详情接口
 */
export interface AuditDetail {
  id: string;
  entrustType: number;
  serviceType: number;
  projectName: string;
  relationUser: string;
  relationPhone: string;
  applicantUser: string;
  submitTime: string;
  status: number;
  // 其他详细字段根据实际需求添加
  [key: string]: any;
}

/**
 * 审核提交参数
 */
export interface AuditSubmitParams {
  id: string;
  result: number; // 3-通过 4-拒绝
  remark?: string; // 审核意见
}

/**
 * 标的信息接口
 */
export interface AuctionItem {
  id: string;
  auctionName: string;
  itemTitle: string;
  itemType: number;
  startPrice: number;
  appraisalPrice: number;
  reservePrice?: number;
  deposit: number;
  quantity: number;
  unit: string;
  province: string;
  city: string;
  district: string;
  address: string;
  description?: string;
  auctionMode: number;
  showCommission?: string;
  freeBidTime?: string;
  timedBidTime?: string;
}

/**
 * 分页查询结果
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}
