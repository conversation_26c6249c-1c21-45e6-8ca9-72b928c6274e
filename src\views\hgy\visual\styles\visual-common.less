// 数据可视化大屏通用样式

// 颜色变量
@primary-color: #12e6db;
@primary-dark: #097884;
@bg-dark: #0a0e27;
@text-light: #ffffff;
@text-secondary: rgba(255, 255, 255, 0.8);
@border-glow: rgba(18, 230, 219, 0.3);

// 发光效果混合器
.glow-effect(@color: @primary-color, @blur: 10px, @opacity: 0.5) {
  box-shadow: 0 0 @blur fade(@color, @opacity * 100%);
}

.text-glow(@color: @primary-color, @blur: 8px, @opacity: 0.6) {
  text-shadow: 0 0 @blur fade(@color, @opacity * 100%);
}

// 渐变背景混合器
.gradient-bg(@start: @primary-color, @end: @primary-dark, @direction: 135deg) {
  background: linear-gradient(@direction, @start 0%, @end 100%);
}

// 异形卡片背景混合器
.card-bg(@bg-image, @border-opacity: 0.3, @bg-opacity: 0.05) {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url(@bg-image) no-repeat center center;
    background-size: cover;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      fade(@primary-color, @bg-opacity * 100%) 0%,
      fade(@primary-color, (@bg-opacity * 0.5) * 100%) 50%,
      fade(@primary-color, @bg-opacity * 100%) 100%
    );
    border: 1px solid fade(@primary-color, @border-opacity * 100%);
    border-radius: 8px;
    z-index: 2;
  }
}

// 数字滚动动画
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 发光动画
@keyframes glow {
  0% {
    box-shadow: 0 0 5px fade(@primary-color, 30%);
  }
  50% {
    box-shadow: 0 0 20px fade(@primary-color, 60%);
  }
  100% {
    box-shadow: 0 0 5px fade(@primary-color, 30%);
  }
}

// 渐变文字动画
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 通用卡片样式
.visual-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    .glow-effect(@primary-color, 15px, 0.4);
  }

  .card-content {
    position: relative;
    z-index: 3;
    padding: 20px;
  }
}

// 通用标题样式
.visual-title {
  color: @text-light;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
  .text-glow(@text-light, 10px, 0.3);

  &.primary {
    color: @primary-color;
    .text-glow(@primary-color, 10px, 0.5);
  }

  &.gradient {
    background: linear-gradient(135deg, @text-light 0%, @primary-color 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
  }
}

// 数值显示样式
.visual-value {
  color: @primary-color;
  font-weight: bold;
  .text-glow(@primary-color, 8px, 0.5);
  animation: countUp 0.8s ease-out;

  .unit {
    color: @text-secondary;
    font-size: 0.8em;
    font-weight: normal;
    margin-left: 4px;
  }
}

// 排名样式
.rank-item {
  transition: all 0.3s ease;

  &:hover {
    background: fade(@primary-color, 10%);
    transform: translateX(5px);
  }

  &.top-rank {
    .rank-number {
      .gradient-bg(@primary-color, @primary-dark);
      .glow-effect(@primary-color, 8px, 0.6);
      animation: pulse 2s ease-in-out infinite;
    }
  }
}

// 图表容器样式
.chart-container {
  .visual-card();

  .chart-title {
    .visual-title();
    font-size: 18px;
  }

  .chart-wrapper {
    height: calc(100% - 50px);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at center, fade(@primary-color, 5%) 0%, transparent 70%);
      pointer-events: none;
      z-index: 1;
    }
  }
}

// 响应式适配
@media screen and (max-width: 1920px) {
  .visual-content {
    transform-origin: center center;
  }
}

@media screen and (max-width: 1440px) {
  .visual-title {
    font-size: 0.9em;
  }

  .visual-value {
    font-size: 0.9em;
  }
}

@media screen and (max-width: 1024px) {
  .visual-main {
    flex-direction: column;
    gap: 15px;
  }

  .visual-left,
  .visual-right {
    flex: none;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: @primary-color;
  border-radius: 2px;

  &:hover {
    background: lighten(@primary-color, 10%);
  }
}

// 加载动画
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid fade(@primary-color, 20%);
  border-top: 3px solid @primary-color;
  border-radius: 50%;
  animation: loading 1s linear infinite;
  margin: 20px auto;
}
