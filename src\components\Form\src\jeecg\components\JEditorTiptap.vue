<template>
  <TiptapEditor v-bind="bindProps" @change="onChange" />
</template>

<script lang="ts">
  import { computed, defineComponent, nextTick } from 'vue';
  import { TiptapEditor } from '/@/components/TiptapEditor';
  import { propTypes } from '/@/utils/propTypes';
  import { Form } from 'ant-design-vue';

  export default defineComponent({
    name: 'JEditorTiptap',
    // 不将 attrs 的属性绑定到 html 标签上
    inheritAttrs: false,
    components: { TiptapEditor },
    props: {
      value: propTypes.string.def(''),
      disabled: propTypes.bool.def(false),
      //是否聚焦
      autoFocus: propTypes.bool.def(true),
      // 编辑器高度
      height: propTypes.string.def('300px'),
      // 占位符
      placeholder: propTypes.string.def('请输入内容...'),
      // 是否隐藏工具栏
      hideToolbar: propTypes.bool.def(false),
    },
    emits: ['change', 'update:value'],
    setup(props, { emit, attrs }) {
      // 合并 props 和 attrs
      const bindProps = computed(() => {
        return {
          modelValue: props.value,
          disabled: props.disabled,
          autoFocus: props.autoFocus,
          height: props.height,
          placeholder: props.placeholder,
          hideToolbar: props.hideToolbar,
          ...attrs,
        };
      });
      
      const formItemContext = Form.useInjectFormItemContext();
      
      // value change 事件
      function onChange(value) {
        emit('change', value);
        emit('update:value', value);
        // update-begin--author:liaozhiyang---date:20240429---for：【QQYUN-9110】组件有值校验没消失
        nextTick(() => {
          formItemContext?.onFieldChange();
        });
        // update-end--author:liaozhiyang---date:20240429---for：【QQYUN-9110】组件有值校验没消失
      }

      return {
        bindProps,
        onChange,
      };
    },
  });
</script>

<style lang="less" scoped></style>
