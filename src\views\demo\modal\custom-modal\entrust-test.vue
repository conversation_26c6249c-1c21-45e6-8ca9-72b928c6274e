<template>
  <div class="entrust-test">
    <h2>自主委托弹窗测试</h2>
    
    <div class="test-buttons">
      <a-button type="primary" @click="showEntrustModal">模拟进入自主委托页面</a-button>
      <a-button @click="goToAppreciationEntrust">跳转到增值委托</a-button>
    </div>

    <!-- 自主委托确认弹窗 -->
    <CustomModal
      v-model:open="confirmModalVisible"
      title="自主委托"
      width="600"
      cancel-text="放弃"
      confirm-text="确认"
      :mask-closable="false"
      @confirm="handleConfirmSelfEntrust"
      @cancel="handleAbandonSelfEntrust"
    >
      <div class="confirm-modal-content">
        <p>我方仅将客户资产信息在我网进行公示，我司不会对客户所发布信息做任何协助</p>
        <p>请确认是否选择自主服务</p>
      </div>
    </CustomModal>

    <div class="test-info">
      <h3>测试说明</h3>
      <ul>
        <li>✅ 点击"模拟进入自主委托页面"按钮会弹出确认弹窗</li>
        <li>✅ 弹窗标题为"自主委托"</li>
        <li>✅ 弹窗内容包含提示信息</li>
        <li>✅ 按钮文本为"放弃"和"确认"</li>
        <li>✅ 点击"放弃"会跳转到增值委托页面</li>
        <li>✅ 点击"确认"会关闭弹窗并显示成功消息</li>
        <li>✅ 弹窗不能通过点击遮罩关闭</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { CustomModal } from '/@/components/Modal'

const router = useRouter()

// 弹窗状态
const confirmModalVisible = ref(false)

// 显示委托确认弹窗
const showEntrustModal = () => {
  confirmModalVisible.value = true
}

// 确认自主委托
const handleConfirmSelfEntrust = () => {
  confirmModalVisible.value = false
  message.success('已选择自主服务')
}

// 放弃自主委托，跳转到增值委托
const handleAbandonSelfEntrust = () => {
  confirmModalVisible.value = false
  message.info('已放弃自主服务，跳转到增值委托页面')
  // 这里应该跳转到增值委托页面
  // router.push('/entrust/appreciationEntrust')
  console.log('应该跳转到增值委托页面')
}

// 直接跳转到增值委托页面
const goToAppreciationEntrust = () => {
  message.info('跳转到增值委托页面')
  // router.push('/entrust/appreciationEntrust')
  console.log('跳转到增值委托页面')
}
</script>

<style lang="less" scoped>
.entrust-test {
  padding: 24px;
}

.test-buttons {
  display: flex;
  gap: 16px;
  margin: 20px 0;
}

.confirm-modal-content {
  text-align: center;
  padding: 20px 0;

  p {
    margin: 12px 0;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
  }
}

.test-info {
  margin-top: 40px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;

  h3 {
    color: #1890ff;
    margin-bottom: 16px;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      padding: 4px 0;
      color: #666;
    }
  }
}
</style>
