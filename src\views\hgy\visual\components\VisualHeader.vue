<template>
  <div class="visual-header">
    <!-- 头部内容 -->
    <div class="header-content">
      <!-- 左侧按钮 -->
      <div class="header-left">
        <div class="nav-button material-select">
          <span class="button-text">请选择物资种类</span>
        </div>
        <div class="nav-button address-select">
          <span class="button-text">选择省市区</span>
        </div>
      </div>

      <!-- 中间标题 -->
      <div class="header-center">
        <h1 class="main-title">灰谷网经营数据可视化大屏</h1>
      </div>

      <!-- 右侧信息 -->
      <div class="header-right">
        <div class="time-info">{{ currentTime }}</div>
        <div class="nav-button return-button">
          <span class="button-text">返回灰谷云</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import dayjs from 'dayjs';

  const currentTime = ref('');

  // 更新时间
  const updateTime = () => {
    currentTime.value = dayjs().format('上午HH:mm YYYY年MM月DD日 dddd');
  };

  let timeInterval: NodeJS.Timeout;

  onMounted(() => {
    updateTime();
    // 每秒更新时间
    timeInterval = setInterval(updateTime, 1000);
  });

  onUnmounted(() => {
    if (timeInterval) {
      clearInterval(timeInterval);
    }
  });
</script>

<style lang="less" scoped>
  .visual-header {
    background: url('@/assets/visual/header.png') no-repeat center center;
    background-size: cover;
    height: 98px;
    width: 100%;
  }

  .header-content {
    position: relative;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 2;
    padding: 0 20px;
  }

  .header-left {
    display: flex;
    gap: 20px;
  }

  .header-center {
    .main-title {
      color: #ffffff;
      font-size: 42px;
      font-weight: bold;
      margin: 0;
      text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
      background: linear-gradient(135deg, #ffffff 0%, #12e6db 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-top: 10px;
    }
  }

  .header-right {
    display: flex;
    gap: 20px;
    align-items: center;

    .time-info {
      color: #13e6db !important;
      font-size: 16px;
      font-weight: 400;
      text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
    }
  }

  .nav-button {
    position: relative;
    width: 135px;
    height: 35px;
    cursor: pointer;
    transition: all 0.3s ease;

    background: linear-gradient(135deg, #13e6db 0%, #0bc4d4 50%, #13e6db 100%);

    clip-path: polygon(0 0, ~'calc(100% - 8px)' 0, 100% 8px, 100% 100%, 8px 100%, 0 ~'calc(100% - 8px)');

    /* 外发光效果 */
    box-shadow:
      0 0 10px rgba(19, 230, 219, 0.4),
      0 0 20px rgba(19, 230, 219, 0.2);

    /* 添加发光动画 */
    animation: buttonGlow 3s ease-in-out infinite;

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 0 15px rgba(19, 230, 219, 0.6),
        0 0 25px rgba(19, 230, 219, 0.3);

      .button-text {
        background: linear-gradient(135deg, rgba(0, 76, 102, 0.9) 0%, rgba(0, 60, 80, 1) 50%, rgba(0, 40, 60, 0.9) 100%);

        box-shadow:
          inset 0 0 15px rgba(19, 230, 219, 0.4),
          inset 0 0 25px rgba(19, 230, 219, 0.2);
      }
    }

    .button-text {
      position: absolute;
      top: 2px;
      left: 2px;
      right: 2px;
      bottom: 2px;

      /* 内部容器背景 */
      background: linear-gradient(135deg, rgba(0, 76, 102, 0.8) 0%, rgba(0, 60, 80, 0.9) 50%, rgba(0, 40, 60, 0.8) 100%);

      /* 内部容器的切角效果（比外部小2px） */
      clip-path: polygon(0 0, ~'calc(100% - 6px)' 0, 100% 6px, 100% 100%, 6px 100%, 0 ~'calc(100% - 6px)');

      /* 内发光效果 */
      box-shadow:
        inset 0 0 10px rgba(19, 230, 219, 0.3),
        inset 0 0 20px rgba(19, 230, 219, 0.1);

      /* 文字内容样式 */
      color: #ffffff;
      font-size: 14px;
      font-weight: 400;
      text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    /* 不同按钮的特殊尺寸 */
    &.address-select {
      width: 225px;
    }
  }

  /* 发光动画效果 */
  @keyframes buttonGlow {
    0% {
      box-shadow:
        0 0 10px rgba(19, 230, 219, 0.4),
        0 0 20px rgba(19, 230, 219, 0.2);
    }
    50% {
      box-shadow:
        0 0 15px rgba(19, 230, 219, 0.6),
        0 0 25px rgba(19, 230, 219, 0.3);
    }
    100% {
      box-shadow:
        0 0 10px rgba(19, 230, 219, 0.4),
        0 0 20px rgba(19, 230, 219, 0.2);
    }
  }
</style>
