# JeecgBoot 项目规则

## 项目基本信息

1. 当前项目使用的是JeecgBoot 企业级低代码开发平台

2. 需要修改的主要是一些组件的样式，因为公司有自己的设计风格，所以需要修改一些组件的样式，比如按钮、输入框、表格等。

3. 修改的时候要注意，该框架有很多可自定义的配置，修改的时候可以在原有的配置上添加配置，但是默认配置需要更改为新增的配置

4. 尽量使用当前框架中封装好的组件，当前框架的文档地址为 https://help.jeecg.com/ui/

5. 需要使用我们自己封装的组件的时候我会告诉你

6. 本项目的主题色为 `#004c66`

## 附件业务类型规范

本项目中的接口中所有的附件字段要求中的业务类型如下：

### 增值委托类型

- **WTJJ**: 代表增值委托中发布竞价委托时的图片附件等等
- **WTZC**: 代表增值委托发布资产处置时的图片附件等等
- **WTCG**: 代表增值委托发布采购委托时的图片附件等等

### 自主委托类型

- **ZZJJ**: 代表自主委托中发布竞价委托时的图片附件等等
- **ZZZC**: 代表自主委托发布资产处置时的图片附件等等
- **ZZCG**: 代表自主委托发布采购委托时的图片附件等等

本项目中所有的附件字段要求中的文件类型如下：

- **image**: 图片类型
- **video**: 视频类型
- **mp3**: 音频类型
- **zip**: 压缩文件类型
- **pdf**: pdf类型
- **ppt**: ppt类型
- **excel**: xls、xlsx类型
- **word**: doc、docx类型
