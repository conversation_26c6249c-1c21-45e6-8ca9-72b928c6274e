# 审核功能组件说明

## 概述

本目录包含了待办事项审核功能的相关组件，支持增值委托和自主委托的审核操作。

## 组件结构

### 1. AuditModal.vue
主审核弹窗组件，负责：
- 根据委托类型（增值/自主）显示不同的审核内容
- 统一的弹窗标题和布局管理
- 审核成功后的回调处理

### 2. AppreciationAudit.vue
增值委托审核组件，支持：
- 基本信息展示
- 根据服务类型（竞价委托、资产处置、采购信息）显示不同详情
- 审核操作（通过/拒绝）
- 审核意见填写

### 3. SelfAudit.vue
自主委托审核组件，支持：
- 基本信息展示
- 根据服务类型显示不同详情
- 发布竞价标的时显示标的列表查看按钮
- 审核操作（通过/拒绝）
- 审核意见填写

### 4. AuctionItemsModal.vue
标的列表弹窗组件，用于：
- 显示自主委托中发布竞价标的的详细列表
- 支持多个标的信息的展示
- 标的类型、价格、位置等信息的格式化显示

## API接口

### 审核相关接口（src/api/manageCenter/audit.ts）

#### 增值委托审核
- `getAppreciationAuditDetail(id)` - 获取增值委托审核详情
- `submitAppreciationAudit(params)` - 提交增值委托审核结果

#### 自主委托审核
- `getSelfAuditDetail(id)` - 获取自主委托审核详情
- `submitSelfAudit(params)` - 提交自主委托审核结果

#### 标的列表
- `getAuctionItemsList(entrustOrderId)` - 获取委托单的标的列表

## 使用方式

在待办列表页面（index.vue）中：

```vue
<template>
  <!-- 审核弹窗 -->
  <AuditModal
    v-model:open="auditModalVisible"
    :record="currentAuditRecord"
    @close="handleCloseAuditModal"
    @success="handleAuditComplete"
  />
</template>

<script>
// 点击审核按钮
function handleAudit(record) {
  currentAuditRecord.value = record;
  auditModalVisible.value = true;
}
</script>
```

## 功能特性

### 1. 委托类型区分
- **增值委托（entrustType: 1）**：显示增值委托相关信息
- **自主委托（entrustType: 2）**：显示自主委托相关信息，竞价委托类型支持查看标的列表
- **供求信息（entrustType: 3）**：暂未实现，显示开发中提示

### 2. 服务类型区分
- **竞价委托（serviceType: 1）**：自主委托时支持查看标的列表
- **资产处置（serviceType: 2）**：显示资产处置相关信息
- **采购信息（serviceType: 3）**：显示采购信息相关内容
- **供应/求购（serviceType: 4/5）**：供求信息相关（暂未实现）

### 3. 审核操作
- 支持通过/拒绝两种审核结果
- 可填写审核意见（可选）
- 表单验证确保审核结果必选
- 提交成功后自动刷新列表

### 4. 标的列表展示
- 仅在自主委托的发布竞价标的时显示
- 支持多个标的信息的详细展示
- 包含标的类型、价格、数量、位置等信息
- 支持标的类型的颜色标签区分

## 注意事项

1. **API地址**：当前使用的是假地址，需要根据实际后端接口进行调整
2. **权限控制**：只有状态为"待审核"（status: 2）的记录才显示审核按钮
3. **错误处理**：API调用失败时会显示错误信息，并在适当情况下使用备用数据
4. **数据格式**：确保后端返回的数据格式与组件期望的接口一致

## 扩展说明

如需添加新的委托类型或服务类型，需要：
1. 在对应的审核组件中添加新的条件分支
2. 更新API接口定义
3. 添加相应的详情展示逻辑
4. 更新类型映射和颜色配置
