import { defHttp } from '/@/utils/http/axios';

/**
 * 待办相关API接口
 */
enum Api {
  // 查询待办列表
  queryPageAll = '/hgy/entrustService/hgyEntrustOrder/workbench/tobedoneQueryPage',
}

/**
 * 待办记录接口
 */
export interface BacklogRecord {
  id: string; // 委托单号
  entrustType: number; // 委托类型 1-增值 2-自主 3供应
  serviceType: number; // 服务类型 1-竞价委托 2-资产处置 3-采购信息 4供应 5求购
  status: number; // 审核状态 2待审 3过审 4拒审
  projectName: string; // 项目名称
  relationUser: string; // 联系人
  relationPhone: string; // 联系电话
  applicantUser: string; // 申请人
  auditUser: string; // 审核人
  submitTime: string; // 提交时间
  auditTime: string; // 审核时间
}

/**
 * 分页查询参数
 */
export interface QueryPageParams {
  entrustOrderId?: string; // 委托单号
  entrustType?: number; // 委托类型 1-增值 2-自主 3供应
  serviceType?: number; // 服务类型 1-竞价委托 2-资产处置 3-采购信息 4供应 5求购
  status?: number; // 审核状态 2待审 3过审 4拒审
  pageNo?: number;
  pageSize?: number;
}

/**
 * 分页查询结果
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 查询待办列表
 * @param params 查询参数
 * @returns Promise<PageResult<BacklogRecord>> 返回分页的待办列表
 */
export const queryPageAll = (params?: QueryPageParams) => {
  // 设置默认参数
  const defaultParams = {
    status: 2, // 默认查询待审核状态
    pageNo: 1,
    pageSize: 10,
    ...params,
  };

  return defHttp.post<PageResult<BacklogRecord>>({
    url: Api.queryPageAll,
    data: defaultParams,
  });
};
