发布竞价委托接口在两个页面中用到了，在增值委托的页面中需要的参数和在自助委托中需要的参数不同，需要的参数结构是：
{
  hgyEntrustOrder{
    /*委托企业ID */
    entrustCompanyId?: number;
    /*委托企业名称 */
    entrustCompanyName?: string;
    /*受委托企业ID */
    onEntrustCompanyId?: number;
    /*受委托企业名称 */
    onEntrustCompanyName?: string;
    /*联系人 */
    relationUser?: string;
    /*联系电话 */
    relationPhone?: string;
  }
  hgyAuctionItemTemp{
    /*委托单ID */
    entrustOrderId?: string;
    /*标的名称 */
    itemName?: string;
    /*标的数量 */
    quantity?: Record<string, unknown>;
    /*拍卖日期 */
    auctionDate?: Record<string, unknown>;
    /*是否设置保留价(0-否 1-是) */
    hasReservePrice?: number;
    /*保留价 */(保留价=1时必填,0时不展示)
    reservePrice?: number;
    /*省份 */
    province?: string;
    /*城市 */
    city?: string;
    /*区县 */
    district?: string;
    /*详细地址 */
    address?: string;
    /*特殊说明 */
    specialNotes?: string;
    /*附件 */（标的图片和附件以及委托单都放在附件中）
    attachmentList?: {
      /*业务类型 */
      bizType?: string;
      /*文件名称 */
      fileName?: string;
      /*文件路径 */
      filePath?: string;
      /*文件大小(字节) */
      fileSize?: number;
      /*文件类型 */
      fileType?: string;
    }[];
  }
  hgyAuction{(只在自主委托中需要)
    /*拍卖会名称 */
    auctionName?: string;
    /*拍卖方式(01-正常 02-减价 03-盲拍 04-混合式报价 05-盲拍指定版) */
    auctionType?: number;
    /*委托企业ID */
    entrustCompanyId?: number;
    /*委托企业名称 */
    entrustCompanyName?: string;
    /*报名截止时间 */
    registerEndTime?: Record<string, unknown>;
    /*开拍时间 */
    startTime?: Record<string, unknown>;
    /*结束方式(01-手动 02-自动) */
    endType?: number;
    /*拍卖形式(01-同步 02-线上) */
    auctionForm?: number;
    /*封面图片路径 单张 */
    coverImage?: string;
    /*拍卖公告 */
    auctionNotice?: string;
    /*拍卖须知 */
    auctionNotes?: string;
    /*重要声明 */
    importantNotice?: string;
    /*标的表 */
    hgyAuctionItemList?: {
      /*标的序号 */
      itemNo?: string;
      /*标的分类(01-物资/设备 02-机动车 03-房产 04-土地 05-其他) */
      itemType?: number;
      /*标的名称 */
      itemName?: string;
      /*标的标题 */
      itemTitle?: string;
      /*省份 */
      province?: string;
      /*城市 */
      city?: string;
      /*区县 */
      district?: string;
      /*详细地址 */
      address?: string;
      /*封面图片路径 */
      coverImage?: string;
      /*起拍价 */
      startPrice?: number;
      /*评估价格 */
      appraisalPrice?: number;
      /*是否设置保留价(0-否 1-是) */
      hasReservePrice?: number;
      /*保留价 */
      reservePrice?: number;
      /*保证金 */
      deposit?: number;
      /*加价幅度 */
      bidIncrement?: number;
      /*标的数量 */
      quantity?: Record<string, unknown>;
      /*计量单位 */
      unit?: string;
      /*展示佣金 */
      showCommission?: string;
      /*拍卖方式(01-总价 02-单价) */
      auctionMode?: number;
      /*自由竞价时间 */
      freeBidTime?: string;
      /*限时竞价时间 */
      timedBidTime?: string;
      /*标的介绍 */
      description?: string;
      /*附件 */(这里面是标的照片)
      hgyAttachmentList?: {
        /*附件ID */
        id?: string;

        /*租户ID */
        tenantId?: number;

        /*用户ID */
        userId?: string;

        /*业务类型 */
        bizType?: string;

        /*业务ID */
        bizId?: string;

        /*文件名称 */
        fileName?: string;

        /*文件路径 */
        filePath?: string;

        /*文件大小(字节) */
        fileSize?: number;

        /*文件类型 */
        fileType?: string;

        /*上传时间 */
        createTime?: Record<string, unknown>;

        /*删除状态 */
        delFlag?: number;

        /*创建人 */
        createBy?: string;

        /*更新人 */
        updateBy?: string;

        /*更新时间 */
        updateTime?: Record<string, unknown>;
      }[];
    }[];

    /*附件表 */
    hgyAttachmentList?: {
      /*业务类型 */
      bizType?: string;
      /*文件名称 */
      fileName?: string;
      /*文件路径 */
      filePath?: string;
      /*文件大小(字节) */
      fileSize?: number;
      /*文件类型 */
      fileType?: string;
    }[];
  }
}

附件中需要的参数中业务类型：
1.WTJJ 代表增值委托中发布竞价委托时的图片附件等等
2.WTZC 代表增值委托发布资产处置时的图片附件等等
3.WTCG 代表增值委托发布采购委托时的图片附件等等
4.ZZJJ 代表自主委托中发布竞价委托时的图片附件等等
5.ZZZC 代表自主委托发布资产处置时的图片附件等等
6.ZZCG 代表自主委托发布采购委托时的图片附件等等
附件中需要的参数中文件类型：
1.image 代表图片类型
2.video 代表视频类型
3.mp3 代表音频类型
4.zip 代表压缩文件类型
5.pdf 代表pdf类型
6.ppt 代表ppt类型
7.excel 代表xls、xlsx类型
8.word 代表doc、docx类型

只有多个文件的字段存入到附件中，例如标的图片，附件，委托单等，封面字段不需要传到附件中，因为封面只需要一张
