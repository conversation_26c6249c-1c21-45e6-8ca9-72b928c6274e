# 服务类型接口调用修改说明

## 修改概述

根据需求，修改了增值委托和自主委托页面的详情获取函数，使其能够根据服务类型调用不同的接口获取数据。

## 修改内容

### 1. 自主委托页面 (`src/views/entrust/selfEntrust/index.vue`)

#### 1.1 添加新的API导入

```typescript
// 引入自主竞价详情API
import { queryOrderAuctionItemById } from '@/api/orderManage/autonomouslyBidding';
```

#### 1.2 修改 `fetchEntrustDetail` 函数

- 根据 `serviceType.value` 判断调用不同的接口
- 服务类型为1时：调用 `queryOrderAuctionItemById` 接口
- 服务类型为2时：调用原有的 `queryEntrustById` 接口

#### 1.3 添加 `handleAuctionDataDisplay` 函数

- 专门处理自主竞价数据（服务类型为1）的回显
- 根据 `EntrustBiddingRecord` 的实际字段结构进行数据映射
- 保持原有的数据回显逻辑不变（服务类型为2）

### 2. 增值委托页面 (`src/views/entrust/appreciationEntrust/index.vue`)

#### 2.1 添加新的API导入

```typescript
// 引入委托竞价详情API
import { queryOrderItemTempById } from '@/api/orderManage/entrustBidding';
```

#### 2.2 修改 `fetchEntrustDetail` 函数

- 根据 `stepOneData.serviceType` 判断调用不同的接口
- 服务类型为1时：调用 `queryOrderItemTempById` 接口
- 服务类型为2时：调用原有的 `queryEntrustById` 接口

#### 2.3 添加 `handleAuctionDataDisplay` 函数

- 专门处理委托竞价数据（服务类型为1）的回显
- 根据增值委托页面的数据结构进行字段映射
- 保持原有的数据回显逻辑不变（服务类型为2）

## 接口映射关系

### 服务类型为1（竞价委托）

- **增值委托页面**：调用 `/hgy/auction/hgyAuctionItemTemp/queryOrderItemTempById`
- **自主委托页面**：调用 `/hgy/auction/hgyAuction/queryOrderAuctionItemById`

### 服务类型为2（资产处置）

- **增值委托页面**：调用 `/hgy/entrustService/hgyAssetEntrust/queryEntrustById`
- **自主委托页面**：调用 `/hgy/entrustService/hgyAssetEntrust/queryEntrustById`

## 数据结构说明

### 服务类型为1时的数据结构

根据 `JJapi.txt` 文件说明，服务类型为1时接口返回的数据结构包含以下三个主要部分：

#### 1. hgyEntrustOrder（委托单信息）

```typescript
{
  entrustCompanyId: number; // 委托企业ID
  entrustCompanyName: string; // 委托企业名称
  onEntrustCompanyId: number; // 受委托企业ID
  onEntrustCompanyName: string; // 受委托企业名称
  relationUser: string; // 联系人
  relationPhone: string; // 联系电话
}
```

#### 2. hgyAuctionItemTemp（拍卖标的临时信息）

```typescript
{
  entrustOrderId: string; // 委托单ID
  itemName: string; // 标的名称
  quantity: Record<string, unknown>; // 标的数量
  auctionDate: Record<string, unknown>; // 拍卖日期
  hasReservePrice: number; // 是否设置保留价(0-否 1-是)
  reservePrice: number; // 保留价
  province: string; // 省份
  city: string; // 城市
  district: string; // 区县
  address: string; // 详细地址
  specialNotes: string; // 特殊说明
  attachmentList: Array<{
    // 附件列表
    bizType: string; // 业务类型
    fileName: string; // 文件名称
    filePath: string; // 文件路径
    fileSize: number; // 文件大小
    fileType: string; // 文件类型
  }>;
}
```

#### 3. hgyAuction（拍卖信息 - 仅自主委托需要）

```typescript
{
  auctionName: string; // 拍卖会名称
  auctionType: number; // 拍卖方式
  entrustCompanyId: number; // 委托企业ID
  entrustCompanyName: string; // 委托企业名称
  hgyAuctionItemList: Array<{
    // 标的表
    itemNo: string; // 标的序号
    itemType: number; // 标的分类
    itemName: string; // 标的名称
    startPrice: number; // 起拍价
    appraisalPrice: number; // 评估价格
    // ... 其他字段
  }>;
  hgyAttachmentList: Array<{
    // 附件表
    bizType: string;
    fileName: string;
    filePath: string;
    fileSize: number;
    fileType: string;
  }>;
}
```

### 数据字段映射

#### 自主委托页面字段映射

```typescript
// 委托单信息映射
stepOneData.entrustInfo.title = hgyEntrustOrder.entrustCompanyName || '';
stepOneData.entrustInfo.type = hgyEntrustOrder.onEntrustCompanyName || '';
stepThreeData.contact.contactName = hgyEntrustOrder.relationUser || '';
stepThreeData.contact.contactPhone = hgyEntrustOrder.relationPhone || '';

// 拍卖标的临时信息映射
stepOneData.basicInfo.assetName = hgyAuctionItemTemp.itemName || '';
stepOneData.basicInfo.quantity = hgyAuctionItemTemp.quantity?.toString() || '';
stepOneData.location.province = hgyAuctionItemTemp.province || '';
stepOneData.materials.specialNote = hgyAuctionItemTemp.specialNotes || '';

// 拍卖信息映射（自主委托特有）
stepOneData.basicInfo.assetNo = hgyAuction.hgyAuctionItemList[0].itemNo || '';
stepOneData.basicInfo.appraisalValue = hgyAuction.hgyAuctionItemList[0].appraisalPrice || 0;
```

#### 增值委托页面字段映射

```typescript
// 委托单信息映射
stepOneData.entrustInfo.title = hgyEntrustOrder.entrustCompanyName || '';
stepTwoData.contactInfo.contactName = hgyEntrustOrder.relationUser || '';

// 拍卖标的临时信息映射
stepOneData.basicInfo.subjectName = hgyAuctionItemTemp.itemName || '';
stepOneData.basicInfo.subjectQuantity = hgyAuctionItemTemp.quantity?.toString() || '';
stepOneData.basicInfo.hasReservePrice = hgyAuctionItemTemp.hasReservePrice ? '1' : '0';
stepOneData.basicInfo.reservePrice = hgyAuctionItemTemp.reservePrice || undefined;
```

## 兼容性保证

1. **向后兼容**：原有的服务类型为2的逻辑完全保持不变
2. **数据结构兼容**：新接口返回的数据结构与提交数据结构一致
3. **错误处理**：保持原有的错误处理机制

## 测试

创建了测试页面 `src/views/entrust/test/ServiceTypeTest.vue` 用于验证不同服务类型的跳转和数据加载功能。

## 业务类型说明

根据 `JJapi.txt` 文件，附件中的业务类型定义如下：

- **WTJJ**: 增值委托中发布竞价委托时的图片附件等
- **WTZC**: 增值委托发布资产处置时的图片附件等
- **WTCG**: 增值委托发布采购委托时的图片附件等
- **ZZJJ**: 自主委托中发布竞价委托时的图片附件等
- **ZZZC**: 自主委托发布资产处置时的图片附件等
- **ZZCG**: 自主委托发布采购委托时的图片附件等

## 文件类型说明

- **image**: 图片类型
- **video**: 视频类型
- **mp3**: 音频类型
- **zip**: 压缩文件类型
- **pdf**: PDF类型
- **ppt**: PPT类型
- **excel**: XLS、XLSX类型
- **word**: DOC、DOCX类型

## 修改验证

### 测试页面

创建了 `src/views/entrust/test/ServiceTypeTest.vue` 测试页面，包含：

1. **API接口测试**：直接测试新接口的数据结构
2. **页面跳转测试**：验证不同服务类型的页面跳转功能
3. **数据结构验证**：检查返回数据是否包含必要字段

### 验证步骤

1. 运行测试页面，点击"测试委托竞价API"和"测试自主竞价API"
2. 检查返回的数据结构是否包含 `hgyEntrustOrder`、`hgyAuctionItemTemp`、`hgyAuction`
3. 测试页面跳转功能，验证参数传递是否正确
4. 在实际编辑页面中验证数据回显是否正常

## 注意事项

1. **数据结构变化**：服务类型为1时，数据结构完全不同于原有的资产处置数据结构
2. **类型处理**：新接口返回的数据使用 `any` 类型处理，避免类型检查错误
3. **附件处理**：需要处理多个来源的附件列表合并
4. **兼容性**：原有服务类型为2的逻辑完全保持不变
5. **测试建议**：建议在生产环境使用前进行充分的端到端测试

## 后续优化建议

1. **类型定义**：可以为新的数据结构创建专门的TypeScript类型定义
2. **错误处理**：增加更详细的错误处理和用户提示
3. **数据验证**：添加数据完整性验证逻辑
4. **性能优化**：考虑数据缓存机制，避免重复请求
