@font-face {
  /* 自定义的字体名车，调用该字体时使用 */
  font-family: "FZZongYi-M05S";
  /* 引用字体包。.ttf后缀不区分大小写，用.TTF也可以 */
  src: url("FZZongYi-M05S.ttf");
}
@font-face {
  font-family: "PingFang Regular";
  src: url("PingFang Regular.ttf");
}
@font-face {
  font-family: "PingFang Medium";
  src: url("PingFang Medium.ttf");
}
@font-face {
  font-family: "PingFang Bold";
  src: url("PingFang Bold.ttf");
}
@font-face {
  font-family: "PingFang Heavy";
  src: url("PingFang Heavy.ttf");
}
@font-face {
  font-family: "DIN Regular";
  src: url("DIN-Regular.otf");
}
@font-face {
  font-family: "DIN Bold";
  src: url("DIN-Bold.otf");
}
@font-face {
  font-family: "DIN BlackItalic";
  src: url("DIN-BlackItalic.otf");
}
@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url("YouSheBiaoTiHei.ttf");
}
