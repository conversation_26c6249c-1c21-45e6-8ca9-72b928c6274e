<template>
  <div class="duration-picker" :class="`duration-picker-${size}`">
    <div class="duration-inputs">
      <div class="duration-input-group">
        <a-select v-model:value="days" :size="size" :options="dayOptions" />
        <span class="duration-unit">天</span>
      </div>
      <div class="duration-input-group">
        <a-select v-model:value="hours" :size="size" :options="hourOptions" />
        <span class="duration-unit">小时</span>
      </div>
      <div class="duration-input-group">
        <a-select v-model:value="minutes" :size="size" :options="minuteOptions" />
        <span class="duration-unit">分钟</span>
      </div>
      <div class="duration-input-group">
        <a-select v-model:value="seconds" :size="size" :options="secondOptions" />
        <span class="duration-unit">秒</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';

  interface Props {
    value?: string;
    placeholder?: string;
    size?: 'small' | 'default' | 'large';
  }

  interface Emits {
    (e: 'update:value', value: string): void;
    (e: 'change', value: string): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    value: '',
    placeholder: '请选择时长',
    size: 'default',
  });

  const emit = defineEmits<Emits>();

  const days = ref(0);
  const hours = ref(0);
  const minutes = ref(0);
  const seconds = ref(0);

  // 生成选项数据
  const dayOptions = Array.from({ length: 11 }, (_, i) => ({
    label: i.toString(),
    value: i,
  }));

  const hourOptions = Array.from({ length: 24 }, (_, i) => ({
    label: i.toString(),
    value: i,
  }));

  const minuteOptions = Array.from({ length: 60 }, (_, i) => ({
    label: i.toString(),
    value: i,
  }));

  const secondOptions = Array.from({ length: 60 }, (_, i) => ({
    label: i.toString(),
    value: i,
  }));

  // 解析时长字符串 (格式: "1天2小时3分钟4秒" 或 "1d2h3m4s")
  const parseValue = (value: string) => {
    if (!value) {
      days.value = 0;
      hours.value = 0;
      minutes.value = 0;
      seconds.value = 0;
      return;
    }

    // 支持中文格式和英文格式
    const dayMatch = value.match(/(\d+)[天d]/);
    const hourMatch = value.match(/(\d+)[小时h]/);
    const minuteMatch = value.match(/(\d+)[分钟m]/);
    const secondMatch = value.match(/(\d+)[秒s]/);

    days.value = dayMatch ? parseInt(dayMatch[1]) : 0;
    hours.value = hourMatch ? parseInt(hourMatch[1]) : 0;
    minutes.value = minuteMatch ? parseInt(minuteMatch[1]) : 0;
    seconds.value = secondMatch ? parseInt(secondMatch[1]) : 0;
  };

  // 格式化时长为字符串
  const formatValue = () => {
    const parts: string[] = [];
    if (days.value > 0) parts.push(`${days.value}天`);
    if (hours.value > 0) parts.push(`${hours.value}小时`);
    if (minutes.value > 0) parts.push(`${minutes.value}分钟`);
    if (seconds.value > 0) parts.push(`${seconds.value}秒`);
    return parts.join('');
  };

  // 更新值
  const updateValue = () => {
    const newValue = formatValue();
    emit('update:value', newValue);
    emit('change', newValue);
  };

  // 监听props.value变化
  watch(
    () => props.value,
    (newValue) => {
      parseValue(newValue);
    },
    { immediate: true }
  );

  // 监听输入变化
  watch([days, hours, minutes, seconds], () => {
    updateValue();
  });
</script>

<style lang="less" scoped>
  .duration-picker {
    .duration-inputs {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
    }

    .duration-input-group {
      display: flex;
      align-items: center;
      gap: 4px;

      :deep(.ant-select) {
        width: 70px !important;

        .ant-select-selector {
          height: 32px !important;
          line-height: 30px !important;
        }
      }

      .duration-unit {
        font-size: 14px;
        color: #666;
        white-space: nowrap;
      }
    }

    &.duration-picker-large {
      .duration-input-group {
        :deep(.ant-select) {
          width: 125px !important;

          .ant-select-selector {
            height: 40px !important;
            line-height: 38px !important;
          }
        }
      }
    }

    &.duration-picker-small {
      .duration-input-group {
        :deep(.ant-select) {
          width: 60px !important;

          .ant-select-selector {
            height: 24px !important;
            line-height: 22px !important;
          }
        }

        .duration-unit {
          font-size: 12px;
        }
      }
    }
  }
</style>
