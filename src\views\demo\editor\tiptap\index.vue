<template>
  <PageWrapper title="Tiptap富文本编辑器示例">
    <div class="p-4">
      <a-card title="基础使用">
        <div class="mb-4">
          <a-button-group>
            <a-button @click="getValue">获取内容</a-button>
            <a-button @click="setValue">设置内容</a-button>
            <a-button @click="clearValue">清空内容</a-button>
            <a-button @click="toggleDisabled">{{ disabled ? '启用' : '禁用' }}</a-button>
          </a-button-group>
        </div>

        <TiptapEditor
          v-model="content"
          :disabled="disabled"
          height="400px"
          placeholder="请输入内容，体验Tiptap富文本编辑器..."
          @change="handleChange"
        />

        <div class="mt-4">
          <h4>实时内容预览：</h4>
          <div class="content-preview" v-html="content"></div>
        </div>

        <div class="mt-4">
          <h4>新增功能说明：</h4>
          <ul class="feature-list">
            <li><strong>字体选择</strong>：支持系统字体、苹方字体系列、DIN字体系列、方正综艺、优设标题黑等专业字体</li>
            <li><strong>字号设置</strong>：支持12px到36px的常用字号选择</li>
            <li><strong>文字颜色</strong>：提供丰富的颜色选择，支持自定义颜色</li>
            <li><strong>背景高亮</strong>：支持文字背景色设置，适合重点标记</li>
            <li><strong>图片上传</strong>：直接点击图片按钮上传文件，自动插入到编辑器中</li>
            <li><strong>工具栏优化</strong>：更直观的工具栏布局，下拉菜单点击触发，操作更精确</li>
          </ul>
        </div>
      </a-card>

      <a-card title="表单集成示例" class="mt-4">
        <BasicForm :schemas="schemas" :labelWidth="120" @submit="handleSubmit" @reset="handleReset" />
      </a-card>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { TiptapEditor } from '/@/components/TiptapEditor';
  import { BasicForm, FormSchema } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();

  // 基础示例
  const content = ref(`
<h2>欢迎使用Tiptap富文本编辑器</h2>
<p>这是一个基于<strong>Tiptap</strong>的现代化富文本编辑器，具有以下特点：</p>
<ul>
  <li>轻量级且功能强大</li>
  <li>完全免费商用（MIT许可证）</li>
  <li>与Vue 3完美集成</li>
  <li>高度可定制</li>
</ul>
<p>您可以尝试以下功能：</p>
<ol>
  <li><strong>文本格式化</strong>：粗体、斜体、下划线、删除线</li>
  <li><em>标题设置</em>：支持H1-H6标题</li>
  <li><u>文本对齐</u>：左对齐、居中、右对齐、两端对齐</li>
  <li>列表功能：有序列表和无序列表</li>
  <li><span style="font-family: Microsoft YaHei; font-size: 18px; color: #ff0000;">字体选择</span>：支持多种字体和字号</li>
  <li><span style="color: #0066ff;">文字颜色</span>和<mark style="background-color: #ffff00;">背景高亮</mark></li>
  <li><strong>图片上传</strong>：点击图片按钮直接上传文件，无需输入URL</li>
</ol>
<h3>本地字体演示</h3>
<p style="font-family: PingFang Regular; font-size: 16px;">苹方常规：这是苹方常规字体的演示文本</p>
<p style="font-family: PingFang Medium; font-size: 16px; color: #1890ff;">苹方中等：这是苹方中等字体的演示文本</p>
<p style="font-family: PingFang Bold; font-size: 16px; color: #52c41a;">苹方加粗：这是苹方加粗字体的演示文本</p>
<p style="font-family: DIN Regular; font-size: 18px; color: #fa8c16;">DIN Regular: This is DIN Regular font demonstration</p>
<p style="font-family: DIN Bold; font-size: 18px; color: #eb2f96;">DIN Bold: This is DIN Bold font demonstration</p>
<p style="font-family: FZZongYi-M05S; font-size: 20px; color: #722ed1;">方正综艺：这是方正综艺字体的演示文本</p>
<p style="font-family: YouSheBiaoTiHei; font-size: 20px; color: #13c2c2;">优设标题黑：这是优设标题黑字体的演示文本</p>
<p style="text-align: center; font-family: KaiTi; font-size: 20px; color: #ff6600;">这是一段居中的楷体橙色文本</p>
<p style="text-align: right; font-family: SimHei; color: #6600ff;">这是一段右对齐的黑体紫色文本</p>
`);

  const disabled = ref(false);

  const getValue = () => {
    createMessage.info(`当前内容长度: ${content.value.length} 字符`);
    console.log('当前内容:', content.value);
  };

  const setValue = () => {
    content.value = `
<h3>这是通过代码设置的内容</h3>
<p>当前时间: ${new Date().toLocaleString()}</p>
<p>这段内容演示了如何通过代码动态设置编辑器内容。</p>
`;
    createMessage.success('内容已设置');
  };

  const clearValue = () => {
    content.value = '';
    createMessage.success('内容已清空');
  };

  const toggleDisabled = () => {
    disabled.value = !disabled.value;
    createMessage.info(`编辑器已${disabled.value ? '禁用' : '启用'}`);
  };

  const handleChange = (value: string) => {
    console.log('内容变化:', value);
  };

  // 表单集成示例
  const schemas: FormSchema[] = [
    {
      field: 'title',
      component: 'Input',
      label: '标题',
      required: true,
      defaultValue: '测试文章',
    },
    {
      field: 'content',
      component: 'JEditorTiptap' as any,
      label: '内容',
      required: true,
      defaultValue: '<p>这是表单中的富文本编辑器</p>',
      componentProps: {
        height: '300px',
        placeholder: '请输入文章内容...',
      },
    },
    {
      field: 'summary',
      component: 'InputTextArea',
      label: '摘要',
      componentProps: {
        rows: 3,
        placeholder: '请输入文章摘要...',
      },
    },
  ];

  const handleSubmit = (values: any) => {
    createMessage.success('表单提交成功');
    console.log('表单数据:', values);
  };

  const handleReset = () => {
    createMessage.info('表单已重置');
  };
</script>

<style lang="less" scoped>
  .content-preview {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 12px;
    background: #fafafa;
    min-height: 100px;

    :deep(h1),
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
      margin: 16px 0 8px 0;
      font-weight: 600;

      &:first-child {
        margin-top: 0;
      }
    }

    :deep(p) {
      margin: 0 0 8px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :deep(ul),
    :deep(ol) {
      margin: 8px 0;
      padding-left: 24px;

      li {
        margin: 4px 0;
      }
    }

    :deep(strong) {
      font-weight: 600;
    }

    :deep(em) {
      font-style: italic;
    }

    :deep(u) {
      text-decoration: underline;
    }

    :deep(s) {
      text-decoration: line-through;
    }
  }

  .feature-list {
    margin: 12px 0;
    padding-left: 20px;

    li {
      margin: 8px 0;
      line-height: 1.6;

      strong {
        color: #1890ff;
      }
    }
  }
</style>
