<template>
  <div class="p-4">
    <BasicTable @register="registerTable" @navigation-change="handleNavigationChange" @export="handleExport">
      <!-- 操作栏 -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownActions(record)" />
      </template>

      <!-- 审核状态列 -->
      <template #auditStatus="{ text }">
        <a-tag :color="getAuditStatusColor(text)">
          {{ getAuditStatusText(text) }}
        </a-tag>
      </template>

      <!-- 标的状态列 -->
      <template #subjectStatus="{ text }">
        <a-tag :color="getSubjectStatusColor(text)">
          {{ getSubjectStatusText(text) }}
        </a-tag>
      </template>

      <!-- 金额格式化 -->
      <template #amount="{ text }">
        <span v-if="text">{{ formatAmount(text) }}</span>
        <span v-else>-</span>
      </template>

      <!-- 溢价率格式化 -->
      <template #premiumRate="{ text }">
        <span v-if="text !== null && text !== undefined">{{ text }}%</span>
        <span v-else>-</span>
      </template>
    </BasicTable>

    <!-- 详情查看弹窗 -->
    <DetailViewModal v-model:open="detailVisible" :record="currentRecord" :entrust-type="2" :service-type="1" @close="handleDetailClose" />
  </div>
</template>

<script lang="ts" setup name="EntrustBidding">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { ActionItem, BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useMethods } from '/@/hooks/system/useMethods';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { queryPageAll, EntrustBiddingRecord, getExportUrl } from '/@/api/orderManage/autonomouslyBidding';
  import { DetailViewModal } from '/@/components/Audit';
  import type { AuditRecord } from '/@/components/Audit/types';

  const { createMessage, createConfirm } = useMessage();
  const { handleExportXls } = useMethods();
  const router = useRouter();

  // 表格列定义
  const columns: BasicColumn[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '委托单号',
      dataIndex: 'entrustOrderId',
      width: 150,
    },
    {
      title: '标的名称',
      dataIndex: 'itemName',
      width: 150,
    },
    {
      title: '标的类型',
      dataIndex: 'itemType',
      width: 100,
    },
    {
      title: '标的地址',
      dataIndex: 'address',
      width: 200,
      ellipsis: true,
    },
    {
      title: '委托单位',
      dataIndex: 'entrustCompanyName',
      width: 150,
    },
    {
      title: '委托时间',
      dataIndex: 'createTime',
      width: 150,
      customRender: ({ text }) => {
        return text ? formatToDateTime(text) : '-';
      },
    },
    {
      title: '竞价时间',
      dataIndex: 'biddingTime',
      width: 150,
      customRender: ({ text }) => {
        return text ? formatToDateTime(text) : '-';
      },
    },
    {
      title: '标的数量',
      dataIndex: 'quantity',
      width: 100,
    },
    {
      title: '计量单位',
      dataIndex: 'unit',
      width: 80,
    },
    {
      title: '保留价',
      dataIndex: 'reservePrice',
      width: 120,
      slots: { customRender: 'amount' },
    },
    {
      title: '起拍价',
      dataIndex: 'startPrice',
      width: 120,
      slots: { customRender: 'amount' },
    },
    {
      title: '成交价',
      dataIndex: 'dealPrice',
      width: 120,
      slots: { customRender: 'amount' },
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      width: 100,
      slots: { customRender: 'status' },
    },
  ];

  // 导航栏配置
  const navigationItems = [
    { key: 'all', label: '全部委托', icon: '' },
    { key: 'draft', label: '未审核', icon: '' },
    { key: 'pending', label: '审核中', icon: '' },
    { key: 'approved', label: '已通过', icon: '' },
    { key: 'rejected', label: '未通过', icon: '' },
  ];

  const activeNavigationKey = ref<string | number>('all');

  // 按钮标签行配置
  const buttonTagItems = [
    { key: 'status1', label: '待处理' },
    { key: 'status2', label: '处理中' },
    { key: 'status3', label: '已完成' },
    { key: 'status4', label: '已取消' },
  ];

  // 存储当前导航的查询参数
  const currentNavParams = ref<any>({});

  // 详情查看弹窗状态
  const detailVisible = ref(false);
  const currentRecord = ref<AuditRecord | null>(null);

  // 处理导航切换
  function handleNavigationChange(key: string | number) {
    activeNavigationKey.value = key;

    // 根据导航key设置不同的查询参数
    let searchParams = {};
    switch (key) {
      case 'draft':
        searchParams = {
          status: 1, // 草稿
        };
        break;
      case 'pending':
        searchParams = {
          status: 2, // 待审核
        };
        break;
      case 'approved':
        searchParams = {
          status: 3, // 审核通过
        };
        break;
      case 'rejected':
        searchParams = {
          status: 4, // 审核拒绝
        };
        break;
      default:
        searchParams = {}; // 全部委托，不设置过滤条件
    }

    // 存储导航参数
    currentNavParams.value = searchParams;
    // 重新加载数据
    reload();
  }

  // 处理导出按钮点击事件
  async function handleExport() {
    try {
      // 获取当前的搜索参数
      let searchParams = {};
      try {
        searchParams = await getForm().validate();
      } catch (error) {
        // 表单验证失败时，使用空参数
        console.warn('表单验证失败，使用空参数导出:', error);
      }

      // 合并导航参数和搜索参数
      const exportParams = {
        ...currentNavParams.value,
        ...searchParams,
      };

      // 处理时间区间参数
      if (exportParams.entrustTimeRange && Array.isArray(exportParams.entrustTimeRange) && exportParams.entrustTimeRange.length === 2) {
        exportParams.entrustTimeStart = exportParams.entrustTimeRange[0];
        exportParams.entrustTimeEnd = exportParams.entrustTimeRange[1];
        delete exportParams.entrustTimeRange;
      }

      if (exportParams.biddingTimeRange && Array.isArray(exportParams.biddingTimeRange) && exportParams.biddingTimeRange.length === 2) {
        exportParams.biddingTimeStart = exportParams.biddingTimeRange[0];
        exportParams.biddingTimeEnd = exportParams.biddingTimeRange[1];
        delete exportParams.biddingTimeRange;
      }

      console.log('导出参数:', exportParams);

      // 使用 JeecgBoot 的导出方法
      await handleExportXls('委托竞价列表', getExportUrl, exportParams);
      createMessage.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      createMessage.error('导出失败');
    }
  }

  // 自定义API调用函数，可以在这里添加额外的参数处理逻辑
  async function customQueryPageAll(params: any) {
    // 处理时间区间参数
    if (params?.entrustTimeRange) {
      params.entrustTimeStart = params.entrustTimeRange.split(',')[0];
      params.entrustTimeEnd = params.entrustTimeRange.split(',')[1];
      delete params.entrustTimeRange;
    }

    // 可以在这里添加默认参数或者参数转换逻辑
    const defaultParams = {
      entrustType: 2, // 默认自主类型
      serviceType: 1, // 默认竞价委托
    };

    // 合并导航参数和搜索表单参数
    const mergedParams = {
      ...params,
      ...defaultParams,
      ...currentNavParams.value, // 添加导航参数
    };

    return queryPageAll(mergedParams);
  }

  // 表格配置
  const [registerTable, { reload, getForm }] = useTable({
    api: customQueryPageAll, // 使用自定义的API函数
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    // 导航栏配置
    showNavigation: true,
    navigationItems,
    activeNavigationKey: activeNavigationKey.value,
    showExportButton: true,
    inset: true,
    actionColumn: {
      width: 300,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    formConfig: {
      labelWidth: 64,
      // 设置表单整体大小，可选值："default" | "small" | "large" | undefined”
      size: 'large',
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'right',
        },
      },
      schemas: [
        {
          field: 'itemName',
          label: '标的名称',
          component: 'Input',
          colProps: { span: 6 },
        },
        {
          field: 'status',
          label: '标的状态',
          component: 'Select',
          componentProps: {
            placeholder: '请选择标的状态',
            options: [
              { label: '未开始', value: 1 },
              { label: '竞价中', value: 2 },
              { label: '已成交', value: 3 },
              { label: '已流拍', value: 4 },
            ],
          },
          colProps: { span: 6 },
        },
        {
          field: 'entrustTimeRange',
          label: '时间区间',
          component: 'RangePicker',
          componentProps: {
            placeholder: ['开始时间', '结束时间'],
            showTime: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
          },
          colProps: { span: 6 },
        },
      ],
    },
  });

  // 操作按钮配置
  function getTableAction(record: EntrustBiddingRecord): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '查看详情',
        onClick: handleViewDetail.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确认删除该委托吗？',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }

  // 下拉操作按钮配置
  function getDropDownActions(record: EntrustBiddingRecord): ActionItem[] {
    return [
      {
        label: '撤拍',
        popConfirm: {
          title: '确认撤拍该委托吗？',
          confirm: handleWithdraw.bind(null, record),
        },
      },
      {
        label: '报名管理',
        onClick: handleRegistrationManage.bind(null, record),
      },
      {
        label: '数企详情',
        onClick: handleCompanyDetail.bind(null, record),
      },
      {
        label: '竞价记录',
        onClick: handleBiddingRecord.bind(null, record),
      },
      {
        label: '结算信息',
        onClick: handleSettlementInfo.bind(null, record),
      },
      {
        label: '成交确认书',
        onClick: handleDealConfirmation.bind(null, record),
      },
      {
        label: '竞买人列表',
        onClick: handleBidderList.bind(null, record),
      },
      {
        label: '工作报告书',
        onClick: handleWorkReport.bind(null, record),
      },
    ];
  }

  // 事件处理函数
  function handleEdit(record: EntrustBiddingRecord) {
    // 跳转到自主委托页面，携带id和服务类型参数
    const id = record.entrustOrderId;
    const serviceType = 1; // 自主竞价服务类型为1

    router.push({
      path: '/entrust/selfEntrust',
      query: {
        id: id,
        serviceType: serviceType,
      },
    });
  }

  function handleViewDetail(record: EntrustBiddingRecord) {
    // 转换数据格式为 AuditRecord
    const auditRecord: AuditRecord = {
      id: record.id,
      entrustType: 2, // 自主委托
      serviceType: 1, // 竞价委托
      status: record.status || 2,
      projectName: record.itemName || '-',
      relationUser: record.relationUser || '-',
      relationPhone: record.relationPhone || '-',
      applicantUser: record.entrustCompanyName || '-',
      auditUser: record.auditUser || '-',
      submitTime: record.createTime || '-',
      auditTime: record.auditTime || '-',
    };

    currentRecord.value = auditRecord;
    detailVisible.value = true;
  }

  // 关闭详情弹窗
  function handleDetailClose() {
    detailVisible.value = false;
    currentRecord.value = null;
  }

  function handleWithdraw(record: EntrustBiddingRecord) {
    createMessage.info('撤拍功能开发中...');
    console.log('撤拍记录:', record);
  }

  function handleDelete(record: EntrustBiddingRecord) {
    createMessage.info('删除功能开发中...');
    console.log('删除记录:', record);
  }

  function handleRegistrationManage(record: EntrustBiddingRecord) {
    createMessage.info('报名管理功能开发中...');
    console.log('报名管理:', record);
  }

  function handleCompanyDetail(record: EntrustBiddingRecord) {
    createMessage.info('数企详情功能开发中...');
    console.log('数企详情:', record);
  }

  function handleBiddingRecord(record: EntrustBiddingRecord) {
    createMessage.info('竞价记录功能开发中...');
    console.log('竞价记录:', record);
  }

  function handleSettlementInfo(record: EntrustBiddingRecord) {
    createMessage.info('结算信息功能开发中...');
    console.log('结算信息:', record);
  }

  function handleDealConfirmation(record: EntrustBiddingRecord) {
    createMessage.info('成交确认书功能开发中...');
    console.log('成交确认书:', record);
  }

  function handleBidderList(record: EntrustBiddingRecord) {
    createMessage.info('竞买人列表功能开发中...');
    console.log('竞买人列表:', record);
  }

  function handleWorkReport(record: EntrustBiddingRecord) {
    createMessage.info('工作报告书功能开发中...');
    console.log('工作报告书:', record);
  }

  // 状态处理函数
  function getAuditStatusText(status: string) {
    const statusMap: Record<string, string> = {
      '0': '草稿',
      '1': '待审核',
      '2': '审核通过',
      '3': '审核拒绝',
    };
    return statusMap[status] || '未知';
  }

  function getAuditStatusColor(status: string) {
    const colorMap: Record<string, string> = {
      '0': 'default',
      '1': 'processing',
      '2': 'success',
      '3': 'error',
    };
    return colorMap[status] || 'default';
  }

  function getSubjectStatusText(status: string) {
    const statusMap: Record<string, string> = {
      '1': '待发布',
      '2': '报名中',
      '3': '竞价中',
      '4': '已流拍',
      '5': '已发布',
      '6': '已成交',
      '7': '已完成',
      '8': '已撤拍',
    };
    return statusMap[status] || '未知';
  }

  function getSubjectStatusColor(status: string) {
    const colorMap: Record<string, string> = {
      '1': 'default',
      '2': 'processing',
      '3': 'warning',
      '4': 'error',
      '5': 'cyan',
      '6': 'success',
      '7': 'green',
      '8': 'red',
    };
    return colorMap[status] || 'default';
  }

  // 金额格式化函数
  function formatAmount(amount: number | string | null | undefined) {
    if (!amount && amount !== 0) return '-';
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(Number(amount));
  }
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 0;
  }
</style>
