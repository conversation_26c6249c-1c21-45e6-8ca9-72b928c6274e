<template>
  <div class="main-kpi-cards">
    <!-- 背景 -->
    <div class="cards-bg"></div>

    <!-- 内容 -->
    <div class="cards-content">
      <!-- 成交总额 -->
      <div class="kpi-card">
        <div class="card-label">成交总额</div>
        <div class="card-value">
          <CountTo :start="0" :end="59645956" :duration="2000" :decimals="0" />
          <span class="card-unit">万</span>
        </div>
      </div>

      <div class="kpi-group">
        <!-- 溢价总额 -->
        <div class="kpi-card">
          <div class="card-label">溢价总额</div>
          <div class="card-value">
            <CountTo :start="0" :end="5956" :duration="2000" :decimals="0" />
            <span class="card-unit">万</span>
          </div>
        </div>

        <!-- 总溢价率 -->
        <div class="kpi-card" style="margin-left: 10px">
          <div class="card-label">总溢价率</div>
          <div class="card-value">
            <CountTo :start="0" :end="95.0" :duration="2000" :decimals="2" />
            <span class="card-unit">%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import CountTo from './CountTo.vue';
</script>

<style lang="less" scoped>
  .main-kpi-cards {
    position: relative;
    width: 768px;
    height: 169px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 60px;
  }

  .cards-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('@/assets/visual/center-bg.png') no-repeat center center;
    background-size: cover;
    z-index: 1;

    /* &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(18, 230, 219, 0.1) 0%, rgba(18, 230, 219, 0.05) 50%, rgba(18, 230, 219, 0.1) 100%);
      border: 1px solid rgba(18, 230, 219, 0.3);
      border-radius: 8px;
    } */
  }

  .cards-content {
    position: relative;
    width: 51%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    z-index: 2;
  }

  .kpi-card,
  .kpi-group {
    flex: 1;
  }

  .kpi-group {
    display: flex;
    margin-left: 20px;
  }

  .card-label {
    color: #ffffff;
    font-size: 22px;
    font-family: 'YouSheBiaoTiHei';
    margin-bottom: 2px;
    opacity: 0.9;
  }

  .card-value {
    display: flex;
    align-items: baseline;
    gap: 4px;

    :deep(.count-to) {
      color: #12e6db;
      font-size: 30px;
      font-family: 'DIN Bold';
      text-shadow: 0 0 15px rgba(18, 230, 219, 0.6);
    }

    .card-unit {
      color: #ffffff;
      font-size: 12px;
      font-family: 'PingFang Bold';
      opacity: 0.8;
    }
  }
</style>
