<template>
  <CustomModal
    v-model:open="visible"
    :title="modalTitle"
    :width="1200"
    :show-footer="false"
    :mask-closable="false"
    @close="handleClose"
  >
    <div class="audit-modal-content">
      <!-- 审核内容区域 -->
      <div class="audit-content">
        <!-- 增值委托审核 -->
        <AppreciationAudit
          v-if="record?.entrustType === 1"
          :record="record"
          :service-type="record?.serviceType"
          @audit-success="handleAuditSuccess"
          @close="handleClose"
        />
        
        <!-- 自主委托审核 -->
        <SelfAudit
          v-else-if="record?.entrustType === 2"
          :record="record"
          :service-type="record?.serviceType"
          @audit-success="handleAuditSuccess"
          @close="handleClose"
        />
        
        <!-- 供求信息审核（暂时不实现） -->
        <div v-else-if="record?.entrustType === 3" class="not-implemented">
          <a-result
            status="info"
            title="供求信息审核功能暂未实现"
            sub-title="该功能正在开发中，敬请期待"
          >
            <template #extra>
              <a-button type="primary" @click="handleClose">关闭</a-button>
            </template>
          </a-result>
        </div>
      </div>
    </div>
  </CustomModal>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { CustomModal } from '/@/components/Modal';
  import { BacklogRecord } from '/@/api/manageCenter/backlog';
  import AppreciationAudit from './AppreciationAudit.vue';
  import SelfAudit from './SelfAudit.vue';

  interface Props {
    open: boolean;
    record: BacklogRecord | null;
  }

  interface Emits {
    (e: 'update:open', value: boolean): void;
    (e: 'close'): void;
    (e: 'success'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const visible = ref(props.open);

  // 监听外部传入的open状态
  watch(
    () => props.open,
    (newValue) => {
      visible.value = newValue;
    }
  );

  // 监听内部visible状态变化
  watch(visible, (newValue) => {
    emit('update:open', newValue);
  });

  // 弹窗标题
  const modalTitle = computed(() => {
    if (!props.record) return '审核';
    
    const entrustTypeMap: Record<number, string> = {
      1: '增值委托',
      2: '自主委托',
      3: '供求信息',
    };
    
    const serviceTypeMap: Record<number, string> = {
      1: '竞价委托',
      2: '资产处置',
      3: '采购信息',
      4: '供应',
      5: '求购',
    };
    
    const entrustTypeName = entrustTypeMap[props.record.entrustType] || '未知';
    const serviceTypeName = serviceTypeMap[props.record.serviceType] || '未知';
    
    return `${entrustTypeName} - ${serviceTypeName} - 审核`;
  });

  // 关闭弹窗
  function handleClose() {
    visible.value = false;
    emit('close');
  }

  // 审核成功
  function handleAuditSuccess() {
    emit('success');
  }
</script>

<style lang="less" scoped>
  .audit-modal-content {
    min-height: 400px;
    
    .audit-content {
      padding: 20px 0;
    }
    
    .not-implemented {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;
    }
  }
</style>
