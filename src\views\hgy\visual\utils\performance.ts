/**
 * 数据可视化大屏性能优化工具
 */

/**
 * 请求动画帧的Promise版本
 */
export function requestAnimationFramePromise(): Promise<number> {
  return new Promise((resolve) => {
    requestAnimationFrame(resolve);
  });
}

/**
 * 延迟执行函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}

/**
 * 批量执行任务，避免阻塞主线程
 */
export async function batchExecute<T>(
  tasks: (() => T)[],
  batchSize: number = 10,
  delayMs: number = 0
): Promise<T[]> {
  const results: T[] = [];
  
  for (let i = 0; i < tasks.length; i += batchSize) {
    const batch = tasks.slice(i, i + batchSize);
    const batchResults = batch.map(task => task());
    results.push(...batchResults);
    
    if (delayMs > 0 && i + batchSize < tasks.length) {
      await delay(delayMs);
    }
  }
  
  return results;
}

/**
 * 内存使用监控
 */
export function getMemoryUsage(): {
  used: number;
  total: number;
  percentage: number;
} | null {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
    };
  }
  return null;
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private marks: Map<string, number> = new Map();
  private measures: Map<string, number> = new Map();

  /**
   * 开始性能标记
   */
  mark(name: string): void {
    this.marks.set(name, performance.now());
    performance.mark(name);
  }

  /**
   * 测量性能
   */
  measure(name: string, startMark: string, endMark?: string): number {
    if (endMark) {
      performance.measure(name, startMark, endMark);
    } else {
      const startTime = this.marks.get(startMark);
      if (startTime) {
        const duration = performance.now() - startTime;
        this.measures.set(name, duration);
        return duration;
      }
    }
    
    const entries = performance.getEntriesByName(name, 'measure');
    if (entries.length > 0) {
      const duration = entries[entries.length - 1].duration;
      this.measures.set(name, duration);
      return duration;
    }
    
    return 0;
  }

  /**
   * 获取所有测量结果
   */
  getAllMeasures(): Map<string, number> {
    return new Map(this.measures);
  }

  /**
   * 清除性能数据
   */
  clear(): void {
    this.marks.clear();
    this.measures.clear();
    performance.clearMarks();
    performance.clearMeasures();
  }

  /**
   * 获取性能报告
   */
  getReport(): {
    marks: string[];
    measures: { name: string; duration: number }[];
    memory: ReturnType<typeof getMemoryUsage>;
  } {
    return {
      marks: Array.from(this.marks.keys()),
      measures: Array.from(this.measures.entries()).map(([name, duration]) => ({
        name,
        duration,
      })),
      memory: getMemoryUsage(),
    };
  }
}

/**
 * 图片预加载器
 */
export class ImagePreloader {
  private loadedImages: Map<string, HTMLImageElement> = new Map();
  private loadingPromises: Map<string, Promise<HTMLImageElement>> = new Map();

  /**
   * 预加载单个图片
   */
  preloadImage(src: string): Promise<HTMLImageElement> {
    // 如果已经加载过，直接返回
    if (this.loadedImages.has(src)) {
      return Promise.resolve(this.loadedImages.get(src)!);
    }

    // 如果正在加载，返回现有的Promise
    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src)!;
    }

    // 开始加载
    const promise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        this.loadedImages.set(src, img);
        this.loadingPromises.delete(src);
        resolve(img);
      };
      
      img.onerror = () => {
        this.loadingPromises.delete(src);
        reject(new Error(`Failed to load image: ${src}`));
      };
      
      img.src = src;
    });

    this.loadingPromises.set(src, promise);
    return promise;
  }

  /**
   * 批量预加载图片
   */
  async preloadImages(srcs: string[]): Promise<HTMLImageElement[]> {
    const promises = srcs.map(src => this.preloadImage(src));
    return Promise.all(promises);
  }

  /**
   * 获取已加载的图片
   */
  getLoadedImage(src: string): HTMLImageElement | null {
    return this.loadedImages.get(src) || null;
  }

  /**
   * 清除缓存
   */
  clear(): void {
    this.loadedImages.clear();
    this.loadingPromises.clear();
  }
}

/**
 * 资源管理器
 */
export class ResourceManager {
  private resources: Map<string, any> = new Map();
  private cleanupFunctions: Map<string, () => void> = new Map();

  /**
   * 注册资源
   */
  register(key: string, resource: any, cleanup?: () => void): void {
    this.resources.set(key, resource);
    if (cleanup) {
      this.cleanupFunctions.set(key, cleanup);
    }
  }

  /**
   * 获取资源
   */
  get<T>(key: string): T | null {
    return this.resources.get(key) || null;
  }

  /**
   * 释放资源
   */
  release(key: string): void {
    const cleanup = this.cleanupFunctions.get(key);
    if (cleanup) {
      cleanup();
      this.cleanupFunctions.delete(key);
    }
    this.resources.delete(key);
  }

  /**
   * 释放所有资源
   */
  releaseAll(): void {
    for (const [key] of this.resources) {
      this.release(key);
    }
  }

  /**
   * 获取资源使用情况
   */
  getUsage(): {
    resourceCount: number;
    cleanupCount: number;
    keys: string[];
  } {
    return {
      resourceCount: this.resources.size,
      cleanupCount: this.cleanupFunctions.size,
      keys: Array.from(this.resources.keys()),
    };
  }
}

/**
 * 创建性能监控实例
 */
export const performanceMonitor = new PerformanceMonitor();

/**
 * 创建图片预加载器实例
 */
export const imagePreloader = new ImagePreloader();

/**
 * 创建资源管理器实例
 */
export const resourceManager = new ResourceManager();

/**
 * 检查浏览器性能
 */
export function checkBrowserPerformance(): {
  isHighPerformance: boolean;
  devicePixelRatio: number;
  hardwareConcurrency: number;
  memory: ReturnType<typeof getMemoryUsage>;
  recommendations: string[];
} {
  const recommendations: string[] = [];
  const memory = getMemoryUsage();
  const devicePixelRatio = window.devicePixelRatio || 1;
  const hardwareConcurrency = navigator.hardwareConcurrency || 4;

  let isHighPerformance = true;

  // 检查设备像素比
  if (devicePixelRatio > 2) {
    recommendations.push('高分辨率屏幕，建议降低图表渲染质量');
    isHighPerformance = false;
  }

  // 检查CPU核心数
  if (hardwareConcurrency < 4) {
    recommendations.push('CPU核心数较少，建议减少动画效果');
    isHighPerformance = false;
  }

  // 检查内存使用
  if (memory && memory.percentage > 80) {
    recommendations.push('内存使用率较高，建议减少数据缓存');
    isHighPerformance = false;
  }

  return {
    isHighPerformance,
    devicePixelRatio,
    hardwareConcurrency,
    memory,
    recommendations,
  };
}

/**
 * 优化配置建议
 */
export function getOptimizationConfig(): {
  enableAnimations: boolean;
  chartRenderMode: 'canvas' | 'svg';
  updateInterval: number;
  batchSize: number;
  cacheSize: number;
} {
  const { isHighPerformance, devicePixelRatio, hardwareConcurrency } = checkBrowserPerformance();

  return {
    enableAnimations: isHighPerformance && hardwareConcurrency >= 4,
    chartRenderMode: devicePixelRatio > 1.5 ? 'canvas' : 'svg',
    updateInterval: isHighPerformance ? 1000 : 3000,
    batchSize: Math.max(5, Math.min(20, hardwareConcurrency * 2)),
    cacheSize: isHighPerformance ? 100 : 50,
  };
}
