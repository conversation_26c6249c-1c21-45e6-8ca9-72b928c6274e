<template>
  <div class="audit-details-example">
    <h2>审核详情显示示例</h2>
    
    <div class="example-section">
      <h3>1. 已审核通过的记录</h3>
      <a-button type="primary" @click="showApprovedAudit">查看审核通过详情</a-button>
    </div>

    <div class="example-section">
      <h3>2. 已审核拒绝的记录</h3>
      <a-button type="primary" @click="showRejectedAudit">查看审核拒绝详情</a-button>
    </div>

    <div class="example-section">
      <h3>3. 待审核的记录</h3>
      <a-button type="primary" @click="showPendingAudit">查看待审核记录</a-button>
    </div>

    <!-- 审核弹窗 -->
    <CommonAuditModal
      v-model:visible="auditVisible"
      :record="currentRecord"
      :service-type="serviceType"
      @audit-success="handleAuditSuccess"
      @close="handleClose"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  import CommonAuditModal from '../src/CommonAuditModal.vue';
  import type { AuditRecord } from '../types';

  const auditVisible = ref(false);
  const serviceType = ref(2); // 资产处置
  const currentRecord = ref<AuditRecord | null>(null);

  // 模拟已审核通过的记录
  const approvedRecord: AuditRecord = {
    id: 'approved-001',
    entrustType: 1, // 增值委托
    serviceType: 2, // 资产处置
    projectName: '办公设备处置项目',
    relationUser: '张三',
    relationPhone: '13800138001',
    applicantUser: '李四',
    submitTime: '2024-01-15 10:30:00',
    status: 3, // 已通过
    // 模拟包含审核详情的数据结构
    hgyEntrustOrder: {
      status: 3,
      auditOpinion: '资产信息完整，处置方案合理，审核通过。'
    }
  };

  // 模拟已审核拒绝的记录
  const rejectedRecord: AuditRecord = {
    id: 'rejected-001',
    entrustType: 2, // 自主委托
    serviceType: 2, // 资产处置
    projectName: '设备报废处置项目',
    relationUser: '王五',
    relationPhone: '13800138002',
    applicantUser: '赵六',
    submitTime: '2024-01-16 14:20:00',
    status: 4, // 已拒绝
    // 模拟包含审核详情的数据结构
    hgyEntrustOrder: {
      status: 4,
      auditOpinion: '资产评估价值不合理，处置时间安排不当，请重新提交申请。'
    }
  };

  // 模拟待审核的记录
  const pendingRecord: AuditRecord = {
    id: 'pending-001',
    entrustType: 1, // 增值委托
    serviceType: 2, // 资产处置
    projectName: '机械设备处置项目',
    relationUser: '孙七',
    relationPhone: '13800138003',
    applicantUser: '周八',
    submitTime: '2024-01-17 09:15:00',
    status: 2, // 待审核
    // 待审核记录没有审核详情
    hgyEntrustOrder: {
      status: 2
    }
  };

  // 显示已审核通过的记录
  function showApprovedAudit() {
    currentRecord.value = approvedRecord;
    serviceType.value = 2;
    auditVisible.value = true;
  }

  // 显示已审核拒绝的记录
  function showRejectedAudit() {
    currentRecord.value = rejectedRecord;
    serviceType.value = 2;
    auditVisible.value = true;
  }

  // 显示待审核的记录
  function showPendingAudit() {
    currentRecord.value = pendingRecord;
    serviceType.value = 2;
    auditVisible.value = true;
  }

  // 审核成功回调
  function handleAuditSuccess() {
    message.success('审核操作成功！');
    auditVisible.value = false;
  }

  // 关闭弹窗
  function handleClose() {
    auditVisible.value = false;
  }
</script>

<style scoped>
  .audit-details-example {
    padding: 24px;
  }

  .example-section {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
  }

  .example-section h3 {
    margin-bottom: 12px;
    color: #333;
  }
</style>
