# 委托竞价和自主竞价编辑功能实现说明

## 功能概述

本次实现了委托竞价和自主竞价的编辑功能，完全参考了 `src/views/orderManage/entrustDispose/index.vue` 页面的编辑功能设计，采用直接跳转的方式，不需要预先调用API接口。

### 2. 页面功能实现

#### 委托竞价编辑功能

- **文件**: `src/views/orderManage/entrustBidding/index.vue`
- **功能**: 点击编辑按钮后跳转到增值委托页面
- **跳转路径**: `/entrust/appreciationEntrust`
- **服务类型**: 1 (竞价委托)

```typescript
async function handleEdit(record: EntrustBiddingRecord) {
  try {
    // 调用查询接口获取详情
    await queryOrderItemTempById(record.id);

    // 查询成功后跳转到增值委托页面进行编辑
    router.push({
      path: '/entrust/appreciationEntrust',
      query: {
        id: record.entrustOrderId, // 使用委托单号
        serviceType: 1, // 竞价委托服务类型为1
      },
    });

    createMessage.success('正在跳转到编辑页面...');
  } catch (error) {
    console.error('编辑操作失败:', error);
    createMessage.error('编辑操作失败，请稍后重试');
  }
}
```

#### 自主竞价编辑功能

- **文件**: `src/views/orderManage/autonomouslyBidding/index.vue`
- **功能**: 点击编辑按钮后跳转到自主委托页面
- **跳转路径**: `/entrust/selfEntrust`
- **服务类型**: 1 (竞价委托)

```typescript
async function handleEdit(record: EntrustBiddingRecord) {
  try {
    // 调用查询接口获取详情
    await queryOrderAuctionItemById(record.id);

    // 查询成功后跳转到自主委托页面进行编辑
    router.push({
      path: '/entrust/selfEntrust',
      query: {
        id: record.entrustOrderId, // 使用委托单号
        serviceType: 1, // 竞价委托服务类型为1
      },
    });

    createMessage.success('正在跳转到编辑页面...');
  } catch (error) {
    console.error('编辑操作失败:', error);
    createMessage.error('编辑操作失败，请稍后重试');
  }
}
```

## 功能流程

### 委托竞价编辑流程

1. 用户在委托竞价列表页面点击"编辑"按钮
2. 系统调用 `queryOrderItemTempById` 接口获取记录详情
3. 接口调用成功后，跳转到增值委托页面 (`/entrust/appreciationEntrust`)
4. 传递参数：委托单号 (`entrustOrderId`) 和服务类型 (1)
5. 在增值委托页面进行编辑操作

### 自主竞价编辑流程

1. 用户在自主竞价列表页面点击"编辑"按钮
2. 系统调用 `queryOrderAuctionItemById` 接口获取记录详情
3. 接口调用成功后，跳转到自主委托页面 (`/entrust/selfEntrust`)
4. 传递参数：委托单号 (`entrustOrderId`) 和服务类型 (1)
5. 在自主委托页面进行编辑操作

## 错误处理

两个编辑功能都包含完善的错误处理机制：

- API调用失败时显示错误提示
- 控制台输出详细错误信息
- 用户友好的错误消息提示

## 测试

创建了测试页面 `src/views/orderManage/test/EditFunctionTest.vue` 用于验证编辑功能：

- 测试委托竞价编辑功能
- 测试自主竞价编辑功能
- 测试API接口调用
- 显示测试结果

## 注意事项

1. **路径配置**: 确保路由配置中存在 `/entrust/appreciationEntrust` 和 `/entrust/selfEntrust` 路径
2. **权限控制**: 编辑功能需要相应的权限控制
3. **数据类型**: 所有接口返回的数据类型都是 `EntrustBiddingRecord`
4. **参数传递**: 跳转时传递的是 `entrustOrderId`（委托单号），不是记录ID
5. **服务类型**: 两个编辑功能的服务类型都固定为 1（竞价委托）

## 依赖项

- Vue Router: 用于页面跳转
- Ant Design Vue: 用于消息提示
- HTTP请求库: 用于API调用

## 后续扩展

如需扩展编辑功能，可以考虑：

1. 添加更多服务类型的支持
2. 增加编辑权限验证
3. 添加编辑历史记录
4. 优化错误处理和用户体验
