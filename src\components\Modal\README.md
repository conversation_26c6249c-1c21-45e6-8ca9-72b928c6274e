# CustomModal 自定义弹窗组件

基于Ant Design Modal封装的自定义弹窗组件，具有固定样式的头部和灵活的内容区域。

## 特性

- ✅ **固定头部样式**：48px高度，渐变背景色
- ✅ **三段式布局**：左侧logo + 中间标题 + 右侧关闭按钮
- ✅ **自定义字体**：标题使用PingFang Bold字体，18px，白色
- ✅ **渐变背景**：从rgba(0, 76, 102, 0.9)到rgba(0, 76, 102, 0.6)
- ✅ **灵活内容**：支持自定义内容和底部按钮
- ✅ **完全兼容**：基于Ant Design Modal，保持所有原有功能

## 安装使用

### 1. 导入组件

```typescript
import { CustomModal } from '/@/components/Modal'
```

### 2. 基础使用

```vue
<template>
  <CustomModal
    v-model:open="visible"
    title="弹窗标题"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <p>这里是弹窗内容</p>
  </CustomModal>
</template>

<script setup>
import { ref } from 'vue'
import { CustomModal } from '/@/components/Modal'

const visible = ref(false)

const handleConfirm = () => {
  console.log('确认')
  visible.value = false
}

const handleCancel = () => {
  console.log('取消')
}
</script>
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| open | boolean | false | 弹窗显示状态 |
| title | string | '弹窗标题' | 弹窗标题文本 |
| width | string \| number | 520 | 弹窗宽度 |
| centered | boolean | true | 是否居中显示 |
| maskClosable | boolean | true | 点击遮罩是否可关闭 |
| destroyOnClose | boolean | false | 关闭时是否销毁内容 |
| getContainer | string \| HTMLElement \| (() => HTMLElement) \| false | false | 指定挂载节点 |
| showFooter | boolean | true | 是否显示底部 |
| showCancelButton | boolean | true | 是否显示取消按钮 |
| showConfirmButton | boolean | true | 是否显示确认按钮 |
| cancelText | string | '取消' | 取消按钮文本 |
| confirmText | string | '确认' | 确认按钮文本 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:open | (value: boolean) | 弹窗显示状态变化 |
| cancel | () | 点击取消按钮或遮罩关闭 |
| confirm | () | 点击确认按钮 |
| close | () | 点击右上角关闭按钮 |

### Slots

| 插槽名 | 说明 |
|--------|------|
| default | 弹窗内容区域 |
| footer | 自定义底部内容 |

## 使用示例

### 1. 基础弹窗

```vue
<CustomModal
  v-model:open="basicVisible"
  title="基础弹窗"
  @confirm="handleConfirm"
  @cancel="handleCancel"
>
  <p>这是基础弹窗内容</p>
</CustomModal>
```

### 2. 自定义宽度

```vue
<CustomModal
  v-model:open="wideVisible"
  title="宽弹窗"
  width="800"
  @confirm="handleConfirm"
>
  <div>更宽的内容区域</div>
</CustomModal>
```

### 3. 无底部按钮

```vue
<CustomModal
  v-model:open="noFooterVisible"
  title="无底部弹窗"
  :show-footer="false"
>
  <p>没有底部按钮的弹窗</p>
</CustomModal>
```

### 4. 自定义底部

```vue
<CustomModal
  v-model:open="customFooterVisible"
  title="自定义底部"
>
  <p>弹窗内容</p>
  
  <template #footer>
    <div class="custom-footer">
      <a-button @click="handleAction1">操作1</a-button>
      <a-button @click="handleAction2">操作2</a-button>
      <a-button type="primary" @click="handleAction3">操作3</a-button>
    </div>
  </template>
</CustomModal>
```

### 5. 表单弹窗

```vue
<CustomModal
  v-model:open="formVisible"
  title="表单弹窗"
  width="600"
  @confirm="handleSubmit"
  @cancel="handleCancel"
>
  <a-form :model="formData" layout="vertical">
    <a-form-item label="姓名">
      <a-input v-model:value="formData.name" />
    </a-form-item>
    <a-form-item label="邮箱">
      <a-input v-model:value="formData.email" />
    </a-form-item>
  </a-form>
</CustomModal>
```

## 样式定制

### 头部样式

头部样式已固定，包含以下特性：

- **高度**：48px
- **背景**：linear-gradient(180deg, rgba(0, 76, 102, 0.9) 0%, rgba(0, 76, 102, 0.6) 100%)
- **布局**：左侧logo + 中间标题 + 右侧关闭按钮
- **标题字体**：PingFang Bold, 18px, #fff

### 内容区域

- **内边距**：24px
- **最小高度**：100px
- **背景色**：#fff

### 底部区域

- **内边距**：16px 24px
- **边框**：顶部1px边框
- **按钮样式**：居中排列，12px间距

## 注意事项

1. **图标依赖**：需要确保项目中存在`model-logo.svg`和`close.svg`图标文件
2. **字体支持**：需要确保项目中已引入PingFang Bold字体
3. **样式覆盖**：如需修改样式，请使用深度选择器`:deep()`
4. **事件处理**：确认按钮不会自动关闭弹窗，需要在事件处理中手动设置

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础弹窗功能
- 固定头部样式
- 自定义内容和底部
