<template>
  <div class="step2">
    <a-form :model="formData" :rules="rules" ref="formRef" :scroll-to-first-error="true">
      <div class="section-content">
        <div v-for="(item, index) in formData.auctionItems" :key="index" class="auction-item">
          <div class="item-header">
            <span class="item-title">标的{{ index + 1 }}</span>
            <!-- 最后一个标的显示新增按钮，其他显示删除按钮 -->
            <a-button v-if="index === formData.auctionItems.length - 1" class="action-btn add-btn" @click="addItem">
              <SvgIcon name="add" :size="12" />
              <span>新增</span>
            </a-button>
            <a-button v-else-if="formData.auctionItems.length > 1" class="action-btn delete-btn" @click="removeItem(index)"> 删除 </a-button>
          </div>

          <!-- 基本信息 -->
          <div class="item-section">
            <div class="section-subtitle">基本信息</div>
            <!-- 第一行：关联拍卖会 -->
            <div class="form-row">
              <a-form-item label="关联拍卖会" :name="['auctionItems', index, 'auctionName']" required class="form-item-third">
                <a-input v-model:value="item.auctionName" placeholder="请输入关联拍卖会" size="large" />
              </a-form-item>
            </div>
            <!-- 第二行：标的标题 -->
            <div class="form-row">
              <a-form-item label="标的标题" :name="['auctionItems', index, 'itemTitle']" required class="form-item-third">
                <a-input v-model:value="item.itemTitle" placeholder="请输入标的标题" size="large" />
              </a-form-item>
            </div>
            <!-- 第三行：标的分类 -->
            <div class="form-row">
              <a-form-item label="标的分类" :name="['auctionItems', index, 'itemType']" required class="form-item-third">
                <a-radio-group v-model:value="item.itemType">
                  <a-radio :value="1">物资/设备</a-radio>
                  <a-radio :value="2">机动车</a-radio>
                  <a-radio :value="3">房产</a-radio>
                  <a-radio :value="4">土地</a-radio>
                  <a-radio :value="5">其他</a-radio>
                </a-radio-group>
              </a-form-item>
            </div>
          </div>

          <!-- 存放位置 -->
          <div class="item-section">
            <div class="section-subtitle">存放位置</div>
            <div class="form-row location-row">
              <a-form-item :name="['auctionItems', index, 'province']" required class="location-item">
                <JAreaSelect
                  v-model:province="item.province"
                  v-model:city="item.city"
                  v-model:area="item.district"
                  placeholder="请选择存放位置"
                  :level="3"
                />
              </a-form-item>
              <a-form-item label="详细地址" :name="['auctionItems', index, 'address']" required class="location-item">
                <a-input v-model:value="item.address" placeholder="请输入详细地址" size="large" />
              </a-form-item>
            </div>
          </div>

          <!-- 标的设置 -->
          <div class="item-section">
            <div class="section-subtitle">标的设置</div>
            <!-- 第一行：起拍价、评估价格 -->
            <div class="form-row basic-three-row">
              <a-form-item label="起拍价" :name="['auctionItems', index, 'startPrice']" required class="basic-three-item">
                <a-input-number
                  v-model:value="item.startPrice"
                  placeholder="请输入起拍价"
                  size="large"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  addon-after="元"
                />
              </a-form-item>
              <a-form-item label="评估价格" :name="['auctionItems', index, 'appraisalPrice']" required class="basic-three-item">
                <a-input-number
                  v-model:value="item.appraisalPrice"
                  placeholder="请输入评估价格"
                  size="large"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  addon-after="元"
                />
              </a-form-item>
            </div>
            <!-- 第二行：保留价、保证金 -->
            <div class="form-row basic-three-row">
              <a-form-item label="保留价" :name="['auctionItems', index, 'reservePrice']" class="basic-three-item">
                <a-input-number
                  v-model:value="item.reservePrice"
                  placeholder="请输入保留价"
                  size="large"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  addon-after="元"
                />
              </a-form-item>
              <a-form-item label="保证金" :name="['auctionItems', index, 'deposit']" required class="basic-three-item">
                <a-input-number
                  v-model:value="item.deposit"
                  placeholder="请输入保证金"
                  size="large"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  addon-after="元"
                />
              </a-form-item>
            </div>
            <!-- 第三行：标的数量、标的单位、是否展示实际数量 -->
            <div class="form-row basic-three-row">
              <a-form-item label="标的数量" :name="['auctionItems', index, 'quantity']" required class="basic-three-item">
                <a-input-number v-model:value="item.quantity" placeholder="请输入标的数量" size="large" style="width: 100%" :min="1" />
              </a-form-item>
              <a-form-item label="标的单位" :name="['auctionItems', index, 'unit']" required class="basic-three-item">
                <a-select v-model:value="item.unit" placeholder="请选择标的单位" size="large">
                  <a-select-option value="件">件</a-select-option>
                  <a-select-option value="台">台</a-select-option>
                  <a-select-option value="套">套</a-select-option>
                  <a-select-option value="个">个</a-select-option>
                  <a-select-option value="批">批</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="是否展示实际数量" :name="['auctionItems', index, 'quantityFlag']" required class="basic-three-item">
                <a-radio-group v-model:value="item.quantityFlag" size="large">
                  <a-radio :value="1">是</a-radio>
                  <a-radio :value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </div>
            <!-- 第四行：展示佣金、拍卖方式 -->
            <div class="form-row basic-three-row">
              <a-form-item label="展示佣金" :name="['auctionItems', index, 'showCommission']" class="basic-three-item">
                <a-input v-model:value="item.showCommission" placeholder="请输入展示佣金" size="large" />
              </a-form-item>
              <a-form-item label="拍卖方式" :name="['auctionItems', index, 'auctionMode']" required class="basic-three-item">
                <a-radio-group v-model:value="item.auctionMode">
                  <a-radio :value="1">总价</a-radio>
                  <a-radio :value="2">单价</a-radio>
                </a-radio-group>
              </a-form-item>
            </div>
            <!-- 第五行：自由竞价、限时竞价 -->
            <div class="form-row basic-three-row">
              <a-form-item label="自由竞价" :name="['auctionItems', index, 'freeBidTime']" class="basic-three-item">
                <DurationPicker v-model:value="item.freeBidTime" placeholder="请选择自由竞价时长" size="large" style="width: 100%" />
              </a-form-item>
              <a-form-item label="限时竞价" :name="['auctionItems', index, 'timedBidTime']" class="basic-three-item">
                <DurationPicker v-model:value="item.timedBidTime" placeholder="请选择限时竞价时长" size="large" style="width: 100%" />
              </a-form-item>
            </div>
          </div>

          <!-- 标的介绍 -->
          <div class="item-section">
            <div class="section-subtitle">标的介绍</div>
            <a-form-item label="拍卖公告" :name="['auctionItems', index, 'description']" class="form-item-full">
              <JEditorTiptap v-model:value="item.description" placeholder="请输入拍卖公告" height="200px" :auto-focus="false" />
            </a-form-item>
          </div>

          <!-- 标的照片 -->
          <div class="item-section">
            <div class="section-subtitle">标的照片</div>
            <!-- 封面图片 -->
            <div class="form-row">
              <a-form-item label="封面图片" :name="['auctionItems', index, 'coverImage']" class="upload-item">
                <div class="upload-container">
                  <JUpload
                    v-model:value="item.coverImage"
                    :multiple="false"
                    :max-count="1"
                    accept="image/*"
                    list-type="picture-card"
                    file-type="image"
                    class="upload-component upload-normal"
                  />
                  <div class="upload-tip">只能上传一张</div>
                </div>
              </a-form-item>
            </div>
            <!-- 标的图片 -->
            <div class="form-row">
              <a-form-item label="标的照片" :name="['auctionItems', index, 'itemImages']" class="upload-item">
                <div class="upload-container">
                  <JUpload
                    v-model:value="item.itemImages"
                    :multiple="true"
                    :max-count="10"
                    accept="image/*"
                    list-type="picture-card"
                    file-type="image"
                    :return-url="false"
                    class="upload-component upload-normal"
                  />
                  <div class="upload-tip">可上传多张图片，最多10张</div>
                </div>
              </a-form-item>
            </div>
          </div>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { reactive, watch, computed, ref } from 'vue';
  import JUpload from '@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import JAreaSelect from '@/components/Form/src/jeecg/components/JAreaSelect.vue';
  import { JEditorTiptap } from '@/components/Form';
  import { SvgIcon } from '/@/components/Icon';
  import DurationPicker from '@/components/DurationPicker';

  // 表单引用
  const formRef = ref();

  // Props 定义
  interface Props {
    modelValue: {
      auctionItems: Array<{
        auctionName: string;
        itemTitle: string;
        itemType: number;
        province: string;
        city: string;
        district: string;
        address: string;
        startPrice: number;
        appraisalPrice: number;
        reservePrice: number;
        deposit: number;
        quantity: number;
        unit: string;
        quantityFlag: number; // 是否展示实际数量 0-否 1-是
        showCommission: string;
        auctionMode: number;
        freeBidTime: string;
        timedBidTime: string;
        description: string;
        coverImage: string;
        itemImages: any[];
      }>;
    };
    serviceType?: number;
  }

  // Emits 定义
  interface Emits {
    (e: 'update:modelValue', value: Props['modelValue']): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 表单数据
  const formData = reactive({ ...props.modelValue });

  // 监听表单数据变化
  watch(
    formData,
    (newVal) => {
      emit('update:modelValue', { ...newVal });
    },
    { deep: true }
  );

  // 监听 props 变化
  watch(
    () => props.modelValue,
    (newVal) => {
      Object.assign(formData, newVal);
    },
    { deep: true }
  );

  // 表单验证规则
  const rules = computed(() => {
    const auctionItemRules = {
      auctionName: [{ required: true, message: '请输入关联拍卖会', trigger: 'blur' }],
      itemTitle: [{ required: true, message: '请输入标的标题', trigger: 'blur' }],
      itemType: [{ required: true, message: '请选择标的分类', trigger: 'change' }],
      province: [{ required: true, message: '请选择存放位置', trigger: 'change' }],
      address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
      startPrice: [{ required: true, message: '请输入起拍价', trigger: 'blur' }],
      appraisalPrice: [{ required: true, message: '请输入评估价格', trigger: 'blur' }],
      deposit: [{ required: true, message: '请输入保证金', trigger: 'blur' }],
      quantity: [{ required: true, message: '请输入标的数量', trigger: 'blur' }],
      unit: [{ required: true, message: '请选择标的单位', trigger: 'change' }],
      quantityFlag: [{ required: true, message: '请选择是否展示实际数量', trigger: 'change' }],
      auctionMode: [{ required: true, message: '请选择拍卖方式', trigger: 'change' }],
      description: [{ required: true, message: '请输入拍卖公告', trigger: 'blur' }],
    };

    // 为每个标的项生成验证规则
    const auctionItemsRules: any = {};
    formData.auctionItems.forEach((_, index) => {
      auctionItemsRules[index] = auctionItemRules;
    });

    return {
      auctionItems: auctionItemsRules,
    };
  });

  // 验证表单
  const validateForm = async (): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.ant-form-item-has-error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return false;
    }
  };

  // 添加标的
  const addItem = () => {
    const newItem = {
      auctionName: '',
      itemTitle: '',
      itemType: 1,
      province: '',
      city: '',
      district: '',
      address: '',
      startPrice: 0,
      appraisalPrice: 0,
      reservePrice: 0,
      deposit: 0,
      quantity: 1,
      unit: '件',
      quantityFlag: 0, // 是否展示实际数量 0-否 1-是
      showCommission: '',
      auctionMode: 1,
      freeBidTime: '',
      timedBidTime: '',
      description: '',
      coverImage: '',
      itemImages: [],
    };
    formData.auctionItems.push(newItem);
  };

  // 删除标的
  const removeItem = (index: number) => {
    if (formData.auctionItems.length > 1) {
      formData.auctionItems.splice(index, 1);
    }
  };

  // 暴露验证方法
  defineExpose({
    validateForm,
  });
</script>

<style lang="less" scoped>
  .step2 {
    // 标的列表样式
    .auction-item {
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      margin-bottom: 16px;
      background-color: #fafafa;

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 30px 0 30px;
        border-bottom: 1px solid #e8e8e8;
        border-radius: 8px 8px 0 0;

        .item-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }

      .item-section {
        padding: 30px 30px 0 30px;
        background-color: #fff;

        &:last-child {
          border-bottom: none;
          border-radius: 0 0 8px 8px;
        }

        .section-subtitle {
          font-size: 14px;
          font-weight: 500;
          color: #666;
          margin-bottom: 16px;
          padding-bottom: 8px;
        }
      }
    }

    .add-item-btn {
      margin-top: 16px;
    }

    .form-section {
      margin-bottom: 32px;
    }

    // 基础表单行布局
    .basic-three-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .basic-three-item {
      flex: 1;
      min-width: 200px;
    }

    // 单个表单项布局
    .form-item-third {
      width: 33.33%;
      min-width: 200px;
    }

    // 位置行布局
    .location-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .location-item {
      flex: 1;
      min-width: 200px;

      :deep(.area-select) {
        .ant-select {
          // 设置选择框文字居中
          .ant-select-selector {
            display: flex;
            align-items: center;

            .ant-select-selection-item {
              text-align: center;
              width: 100%;
              display: flex;
              align-items: center;
            }

            .ant-select-selection-placeholder {
              text-align: center;
              width: 100%;
              display: flex;
              align-items: center;
            }
          }
        }
      }
    }

    // 上传组件样式
    .upload-container {
      display: flex;
      align-items: flex-end;
      gap: 12px;
    }

    .upload-tip {
      font-size: 14px;
      color: #999;
      line-height: 1.4;
      align-self: flex-end;
      flex: 1;
    }

    .upload-item {
      width: 100%;
      margin-bottom: 0;

      .upload-container {
        .upload-tip {
          margin-top: 8px;
          color: #999;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }

    .upload-normal {
      cursor: pointer;
      flex-shrink: 0;

      :deep(.ant-upload-select) {
        width: 100px !important;
        height: 100px !important;
        background-color: #f2f2f2 !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;

        &::before,
        &::after {
          display: none !important;
        }

        .ant-upload {
          width: 100% !important;
          height: 100% !important;
          background-color: #f2f2f2 !important;
          border: none !important;
          border-radius: 4px !important;
          position: relative !important;
          overflow: hidden !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          &::before,
          &::after {
            display: none !important;
          }

          .anticon,
          span,
          .ant-upload-text,
          .ant-upload-hint,
          * {
            display: none !important;
          }

          &::after {
            content: '+' !important;
            width: 22px !important;
            height: 21px !important;
            font-size: 18px !important;
            color: #ddd !important;
            font-weight: 300 !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 10 !important;
            pointer-events: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: transparent !important;
            border: none !important;
            line-height: 1 !important;
          }
        }

        &:hover {
          background-color: #e8e8e8 !important;
          border-color: #bbb !important;

          .ant-upload {
            background-color: #e8e8e8 !important;

            &::after {
              color: #004c66 !important;
            }
          }
        }
      }
    }

    // 标的项样式
    .auction-item {
      border-radius: 8px;
      margin-bottom: 24px;
      background-color: #fff;

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 12px;

        .item-title {
          font-size: 18px;
          font-weight: 600;
          color: #262626;
        }

        .action-btn {
          &.add-btn {
            background-color: #fff;
            color: #666;
            border-color: #ddd;
            font-size: 12px;
            padding: 0;
            width: 61px;
            height: 27px;

            &:hover {
              background-color: #f0f0f0;
              border-color: #f0f0f0;
            }

            span {
              line-height: 1;
              margin-left: 5px;
            }
          }

          &.delete-btn {
            color: #666;
            border: none;

            &:hover {
              color: #ff4d4f;
            }
          }
        }
      }

      .item-section {
        .section-subtitle {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 16px;
          padding-bottom: 8px;
          display: flex;
          align-items: center;
          &::before {
            content: '';
            display: block;
            width: 4px;
            height: 18px;
            margin-right: 8px;
            background-color: #004c66;
          }
        }
      }
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 16px;
      padding-bottom: 8px;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: block;
        width: 4px;
        height: 18px;
        margin-right: 8px;
        background-color: #004c66;
      }
    }

    // 表单项样式调整
    :deep(.ant-form-item) {
      margin-bottom: 16px;
      align-items: flex-start;

      .ant-form-item-label {
        text-align: right;
        width: auto;
        min-width: 90px;
        padding-right: 0;
        display: flex;
        align-items: center;
        justify-content: end;
        height: 40px;

        label {
          color: #666;
          font-size: 16px;
          font-weight: 400;
          line-height: 1;

          &::after {
            content: '';
            margin: 0;
          }
        }
      }

      .ant-form-item-control {
        flex: 1;
        margin-left: 10px;
      }

      .ant-form-item-control-input {
        min-height: 40px;
      }

      .ant-select .ant-select-selector,
      .ant-picker {
        height: 40px !important;
        line-height: 40px !important;
      }

      .ant-input-number-input {
        height: 38px !important;
      }
    }

    // JUpload 组件样式调整
    :deep(.upload-normal) {
      .ant-upload-select-picture-card {
        width: 104px;
        height: 104px;
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    // 输入框样式
    :deep(.ant-input),
    :deep(.ant-select-selector),
    :deep(.ant-picker),
    :deep(.ant-input-number),
    :deep(.ant-textarea) {
      border-radius: 6px;
    }
  }
</style>
