<template>
  <div class="step2">
    <a-form :model="formData.contact" :rules="rules" ref="formRef" :scroll-to-first-error="true">
      <!-- 联系人信息板块 -->
      <div class="form-section">
        <div class="section-title">联系人信息</div>
        <div class="section-content">
          <div class="form-row contact-row">
            <a-form-item label="联系人姓名" name="contactName" required class="contact-item">
              <a-input v-model:value="formData.contact.contactName" placeholder="请输入联系人姓名" size="large" />
            </a-form-item>
            <a-form-item label="联系电话" name="contactPhone" required class="contact-item">
              <a-input v-model:value="formData.contact.contactPhone" placeholder="请输入联系电话" :maxlength="11" size="large" />
            </a-form-item>
          </div>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { reactive, watch, computed, ref } from 'vue';

  // Props 定义
  interface Props {
    modelValue: {
      contact: {
        contactName: string;
        contactPhone: string;
      };
    };
  }

  // Emits 定义
  interface Emits {
    (e: 'update:modelValue', value: Props['modelValue']): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 表单数据
  const formData = reactive({ ...props.modelValue });

  // 监听表单数据变化
  watch(
    formData,
    (newVal) => {
      emit('update:modelValue', { ...newVal });
    },
    { deep: true }
  );

  // 监听 props 变化
  watch(
    () => props.modelValue,
    (newVal) => {
      Object.assign(formData, newVal);
    },
    { deep: true }
  );

  // 表单验证规则
  const rules = computed(() => ({
    contactName: [
      { required: true, message: '请输入联系人姓名', trigger: 'blur' },
      { min: 2, max: 20, message: '联系人姓名长度应在2-20个字符之间', trigger: 'blur' },
    ],
    contactPhone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
    ],
  }));

  // 表单引用
  const formRef = ref();

  // 验证表单
  const validateForm = async (): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.ant-form-item-has-error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return false;
    }
  };

  // 暴露验证方法
  defineExpose({
    validateForm,
  });
</script>

<style lang="less" scoped>
  .step2 {
    .form-section {
      margin-bottom: 32px;
      background: #fafafa;
      border-radius: 8px;
      padding: 24px;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 24px;
        padding-bottom: 12px;
        border-bottom: 2px solid #004c66;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 60px;
          height: 2px;
          background: #004c66;
        }
      }

      .section-content {
        .form-row {
          display: flex;
          gap: 24px;
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          // 联系人信息行
          &.contact-row {
            .contact-item {
              flex: 1;
              margin-bottom: 0;
            }
          }
        }
      }
    }

    // 表单项标签样式
    :deep(.ant-form-item-label > label) {
      font-weight: 500;
      color: #262626;
    }

    // 必填项标记样式
    :deep(.ant-form-item-required::before) {
      color: #ff4d4f;
    }

    // 输入框样式
    :deep(.ant-input) {
      border-radius: 6px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .step2 {
      .form-section {
        padding: 16px;
        margin-bottom: 24px;

        .section-title {
          font-size: 16px;
          margin-bottom: 16px;
        }

        .section-content {
          .form-row {
            flex-direction: column;
            gap: 12px;
            margin-bottom: 16px;

            &.contact-row {
              .contact-item {
                margin-bottom: 16px;

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
