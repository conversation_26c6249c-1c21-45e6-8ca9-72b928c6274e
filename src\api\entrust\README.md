# 拍卖委托接口使用说明

## 模块概述

本模块提供拍卖委托相关的API接口，主要用于处理竞价委托的发布、查询等功能。支持增值委托和自主委托两种模式。

## 文件结构

```
src/api/entrust/
├── auction.ts    # 拍卖相关API接口
├── types.ts      # TypeScript类型定义
└── README.md     # 使用说明文档
```

## 主要接口

### 1. 发布竞价委托（新版本 - 增值委托）

**接口名称：** `addAuctionItemTemp`

**接口地址：** `POST /hgy/auction/hgyAuctionItemTemp/addAuctionItemTemp`

**参数结构：** 使用新的嵌套结构，包含委托单信息、拍卖标的临时信息和拍卖信息（可选）

**使用示例：**

```typescript
import { addAuctionItemTemp } from '@/api/entrust/auction';
import type { AddAuctionItemTempParams } from '@/api/entrust/types';

const submitData: AddAuctionItemTempParams = {
  // 委托单信息
  hgyEntrustOrder: {
    relationUser: '张三', // 联系人
    relationPhone: '13800138000', // 联系电话
    // 其他字段由后端处理
  },
  
  // 拍卖标的临时信息
  hgyAuctionItemTemp: {
    itemName: '测试标的名称', // 标的名称
    quantity: 100, // 标的数量
    auctionDate: '2024-01-01', // 拍卖日期
    hasReservePrice: 1, // 是否设置保留价(0-否 1-是)
    reservePrice: 50000, // 保留价
    province: '北京市', // 省份
    city: '北京市', // 城市
    district: '朝阳区', // 区县
    address: '测试详细地址', // 详细地址
    specialNotes: '特殊说明', // 特殊说明
    attachmentList: [
      {
        bizType: 'WTJJ', // 业务类型：增值委托中发布竞价委托
        fileName: 'image1.jpg',
        filePath: '/uploads/images/image1.jpg',
        fileSize: 1024000,
        fileType: 'image',
      },
      // 更多附件...
    ],
  },
  
  // 拍卖信息（增值委托中不需要，只在自主委托中需要）
  // hgyAuction: { ... },
};

try {
  const result = await addAuctionItemTemp(submitData);
  console.log('发布成功:', result);
} catch (error) {
  console.error('发布失败:', error);
}
```

### 2. 发布竞价委托（兼容旧版本）

**接口名称：** `addAuctionItemTempLegacy`

**使用示例：**

```typescript
import { addAuctionItemTempLegacy } from '@/api/entrust/auction';
import type { LegacyAddAuctionItemTempParams, ServiceTypeEnum } from '@/api/entrust/types';

const submitData: LegacyAddAuctionItemTempParams = {
  // 委托基本信息
  title: '测试委托标题',
  type: '竞价委托',
  description: '这是一个测试委托描述',
  
  // 发布竞价委托字段
  subjectName: '测试标的',
  subjectQuantity: '100',
  measurementUnit: '件',
  auctionDate: '2024-01-01',
  hasReservePrice: 'yes',
  
  // 位置信息
  province: '110000',
  city: '110100',
  area: '110101',
  detailAddress: '北京市朝阳区测试地址',
  latitude: '39.9042',
  longitude: '116.4074',
  
  // 资料信息
  images: [],
  attachments: [],
  entrustDocument: [],
  specialNote: '特殊说明',
  
  // 联系人信息
  contactName: '张三',
  contactPhone: '13800138000',
  
  // 服务类型
  serviceType: ServiceTypeEnum.AUCTION,
};

try {
  const result = await addAuctionItemTempLegacy(submitData);
  console.log('发布成功:', result);
} catch (error) {
  console.error('发布失败:', error);
}
```

### 3. 获取拍卖列表

**接口名称：** `getAuctionList`

**接口地址：** `GET /hgy/auction/list`

**使用示例：**

```typescript
import { getAuctionList } from '@/api/entrust/auction';

const params = {
  pageNo: 1,
  pageSize: 10,
  keyword: '搜索关键词',
};

try {
  const result = await getAuctionList(params);
  console.log('拍卖列表:', result);
} catch (error) {
  console.error('获取列表失败:', error);
}
```

### 4. 获取拍卖详情

**接口名称：** `getAuctionDetail`

**接口地址：** `GET /hgy/auction/detail/{id}`

**使用示例：**

```typescript
import { getAuctionDetail } from '@/api/entrust/auction';

try {
  const result = await getAuctionDetail('auction-id-123');
  console.log('拍卖详情:', result);
} catch (error) {
  console.error('获取详情失败:', error);
}
```

## 数据类型说明

### 枚举类型

#### ServiceTypeEnum - 服务类型
```typescript
export enum ServiceTypeEnum {
  AUCTION = 1,        // 发布竞价委托
  ASSET_DISPOSAL = 2, // 发布资产处置
}
```

#### BizTypeEnum - 业务类型（附件）
```typescript
export enum BizTypeEnum {
  WTJJ = 'WTJJ', // 增值委托中发布竞价委托时的图片附件等等
  WTZC = 'WTZC', // 增值委托发布资产处置时的图片附件等等
  WTCG = 'WTCG', // 增值委托发布采购委托时的图片附件等等
  ZZJJ = 'ZZJJ', // 自主委托中发布竞价委托时的图片附件等等
  ZZZC = 'ZZZC', // 自主委托发布资产处置时的图片附件等等
  ZZCG = 'ZZCG', // 自主委托发布采购委托时的图片附件等等
}
```

#### FileTypeEnum - 文件类型
```typescript
export enum FileTypeEnum {
  IMAGE = 'image', // 图片类型
  VIDEO = 'video', // 视频类型
  MP3 = 'mp3',     // 音频类型
  ZIP = 'zip',     // 压缩文件类型
  PDF = 'pdf',     // pdf类型
  PPT = 'ppt',     // ppt类型
  EXCEL = 'excel', // xls、xlsx类型
  WORD = 'word',   // doc、docx类型
}
```

### 主要接口类型

#### AddAuctionItemTempParams - 新版本参数结构

包含三个主要部分：
- **hgyEntrustOrder：** 委托单信息
- **hgyAuctionItemTemp：** 拍卖标的临时信息
- **hgyAuction：** 拍卖信息（可选，只在自主委托中需要）

#### LegacyAddAuctionItemTempParams - 兼容旧版本参数结构

扁平化的参数结构，包含：
- **委托基本信息：** title, type, description
- **标的信息：** subjectName, subjectQuantity, measurementUnit, auctionDate, hasReservePrice
- **位置信息：** province, city, area, detailAddress, latitude, longitude
- **资料信息：** images, attachments, entrustDocument, specialNote
- **联系人信息：** contactName, contactPhone
- **服务类型：** serviceType

## 注意事项

1. **版本兼容性：** 新版本使用嵌套结构，旧版本使用扁平结构，根据需要选择合适的接口
2. **委托类型区分：** 增值委托和自主委托使用相同接口，但参数结构略有不同
3. **附件处理：** 文件上传需要先调用文件上传接口获取文件路径
4. **业务类型：** 附件的 `bizType` 字段需要根据委托类型和业务场景正确设置
5. **数据验证：** 所有接口调用都需要用户登录状态
6. **坐标信息：** 使用WGS84坐标系
7. **手机号格式：** 需要符合中国大陆手机号规范

## 在组件中的使用

具体的使用示例可以参考以下文件：
- **增值委托：** `src/views/entrust/entrustPublish/AppreciationEntrust.vue`
- **自主委托：** 待实现

## 更新日志

- **v2.0：** 新增嵌套参数结构，支持增值委托和自主委托
- **v1.0：** 初始版本，使用扁平参数结构