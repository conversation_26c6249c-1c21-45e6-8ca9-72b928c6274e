<template>
  <div class="appreciation-audit">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="加载中..." />
    </div>

    <!-- 审核内容 -->
    <div v-else class="audit-container">
      <!-- 基本信息展示 -->
      <div class="info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">委托单号：</span>
            <span class="value">{{ auditData?.id || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">项目名称：</span>
            <span class="value">{{ auditData?.projectName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系人：</span>
            <span class="value">{{ auditData?.relationUser || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系电话：</span>
            <span class="value">{{ auditData?.relationPhone || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">申请人：</span>
            <span class="value">{{ auditData?.applicantUser || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">提交时间：</span>
            <span class="value">{{ formatDateTime(auditData?.submitTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 根据服务类型显示不同的详细信息 -->
      <!-- 竞价委托 -->
      <div v-if="serviceType === 1" class="detail-section">
        <h3 class="section-title">竞价委托详情</h3>
        <div class="detail-content">
          <p>竞价委托相关信息展示区域</p>
          <!-- 这里可以根据实际需求添加更多字段 -->
        </div>
      </div>

      <!-- 资产处置 -->
      <div v-else-if="serviceType === 2" class="detail-section">
        <h3 class="section-title">资产处置详情</h3>
        <div class="detail-content">
          <p>资产处置相关信息展示区域</p>
          <!-- 这里可以根据实际需求添加更多字段 -->
        </div>
      </div>

      <!-- 采购信息 -->
      <div v-else-if="serviceType === 3" class="detail-section">
        <h3 class="section-title">采购信息详情</h3>
        <div class="detail-content">
          <p>采购信息相关信息展示区域</p>
          <!-- 这里可以根据实际需求添加更多字段 -->
        </div>
      </div>

      <!-- 审核操作区域 -->
      <div class="audit-actions">
        <h3 class="section-title">审核操作</h3>
        <div class="audit-form">
          <a-form ref="auditFormRef" :model="auditForm" :rules="auditRules" layout="vertical">
            <a-form-item label="审核结果" name="result" required>
              <a-radio-group v-model:value="auditForm.result">
                <a-radio :value="3">通过</a-radio>
                <a-radio :value="4">拒绝</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="审核意见" name="remark">
              <a-textarea v-model:value="auditForm.remark" placeholder="请输入审核意见" :rows="4" :max-length="500" show-count />
            </a-form-item>
          </a-form>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" :loading="submitting" @click="handleSubmit"> 确认审核 </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import type { FormInstance } from 'ant-design-vue';
  import { BacklogRecord } from '/@/api/manageCenter/backlog';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { getAppreciationAuditDetail, submitAppreciationAudit } from '/@/api/manageCenter/audit';
  import dayjs from 'dayjs';

  interface Props {
    record: BacklogRecord | null;
    serviceType: number;
  }

  interface Emits {
    (e: 'auditSuccess'): void;
    (e: 'close'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const loading = ref(false);
  const submitting = ref(false);
  const auditFormRef = ref<FormInstance>();
  const auditData = ref<any>(null);

  // 审核表单
  const auditForm = reactive({
    result: undefined as number | undefined,
    remark: '',
  });

  // 表单验证规则
  const auditRules = {
    result: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  };

  // 格式化时间
  function formatDateTime(time: string | undefined) {
    return time ? formatToDateTime(dayjs(time)) : '-';
  }

  // 获取审核详情数据
  async function fetchAuditData() {
    if (!props.record?.id) return;

    loading.value = true;
    try {
      // 调用实际的API获取详细数据
      const response = await getAppreciationAuditDetail(props.record.id);
      auditData.value = response;
    } catch (error) {
      console.error('获取审核详情失败:', error);
      message.error('获取审核详情失败');
      // 如果API调用失败，使用传入的record数据作为备用
      auditData.value = props.record;
    } finally {
      loading.value = false;
    }
  }

  // 提交审核
  async function handleSubmit() {
    try {
      await auditFormRef.value?.validate();

      if (!props.record?.id) {
        message.error('委托单号不能为空');
        return;
      }

      submitting.value = true;

      // 调用实际的审核API
      await submitAppreciationAudit({
        id: props.record.id,
        result: auditForm.result!,
        remark: auditForm.remark,
      });

      message.success('审核提交成功');
      emit('auditSuccess');
    } catch (error) {
      console.error('审核提交失败:', error);
      if (error !== 'validation failed') {
        message.error('审核提交失败');
      }
    } finally {
      submitting.value = false;
    }
  }

  // 取消审核
  function handleCancel() {
    emit('close');
  }

  onMounted(() => {
    fetchAuditData();
  });
</script>

<style lang="less" scoped>
  .appreciation-audit {
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
    }

    .audit-container {
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
        border-left: 4px solid #1890ff;
        padding-left: 12px;
      }

      .info-section {
        margin-bottom: 24px;

        .info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 16px;

          .info-item {
            display: flex;

            .label {
              font-weight: 500;
              color: #666;
              min-width: 80px;
            }

            .value {
              color: #333;
              flex: 1;
            }
          }
        }
      }

      .detail-section {
        margin-bottom: 24px;

        .detail-content {
          padding: 16px;
          background: #f5f5f5;
          border-radius: 6px;
          color: #666;
        }
      }

      .audit-actions {
        .audit-form {
          margin-bottom: 24px;
        }

        .action-buttons {
          display: flex;
          justify-content: center;
          gap: 12px;
        }
      }
    }
  }
</style>
