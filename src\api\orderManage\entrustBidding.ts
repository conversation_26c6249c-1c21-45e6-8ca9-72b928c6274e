import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

/**
 * 委托竞价相关API接口
 */
enum Api {
  // 查询列表
  queryPageAll = '/hgy/auction/hgyAuctionItemTemp/queryOrderList',
  // 新增
  add = '/hgy/entrustService/hgyAssetEntrust/add',
  // 编辑
  edit = '/hgy/entrustService/hgyAssetEntrust/edit',
  // 删除单个
  deleteOne = '/hgy/entrustService/hgyAssetEntrust/delete',
  // 批量删除
  deleteBatch = '/hgy/entrustService/hgyAssetEntrust/deleteBatch',
  // 撤拍
  withdraw = '/hgy/entrustService/hgyAssetEntrust/withdraw',
  // 导出
  exportXls = '/hgy/entrustService/hgyAssetEntrust/exportXls',
  // 导入
  importExcel = '/hgy/entrustService/hgyAssetEntrust/importExcel',
  // 委托竞价编辑查询接口
  queryOrderItemTempById = '/hgy/auction/hgyAuctionItemTemp/queryOrderItemTempById',
}

/**
 * 委托竞价记录接口
 */
export interface EntrustBiddingRecord {
  id: string;
  tenantId: number;
  userId: string;
  entrustCompanyId: number;
  entrustCompanyName: string;
  onEntrustCompanyId: number;
  onEntrustCompanyName: string;
  entrustType: number;
  serviceType: number;
  status: number;
  relationUser: string;
  relationPhone: string;
  auditOpinion: string;
  auditTime: string;
  auditUser: string;
  entrustOrderId: string;
  auctionId: string;
  itemNo: string;
  itemType: number;
  itemName: string;
  itemTitle: string;
  province: string;
  city: string;
  district: string;
  address: string;
  coverImage: string;
  startPrice: number;
  appraisalPrice: number;
  hasReservePrice: number;
  reservePrice: number;
  deposit: number;
  bidIncrement: number;
  quantity: number;
  actualQuantity: number;
  unit: string;
  showCommission: string;
  auctionMode: number;
  auctionDate: string;
  freeBidTime: string;
  timedBidTime: string;
  description: string;
  specialNotes: string;
  createTime: string;
  updateTime: string;
  delFlag: number;
  createBy: string;
  updateBy: string;
}

/**
 * 分页查询参数
 */
export interface QueryPageParams {
  pageNo?: number;
  pageSize?: number;
  /* entrustOrderId?: string;
  subjectName?: string;
  entrustCompany?: string;
  auditStatus?: string;
  [key: string]: any; */
  itemName?: string; // 标的名称
  status?: string; // 状态
  auctionDateEnd?: string; // 拍卖结束时间
  entrustStatus?: string; // 拍卖开始时间
}

/**
 * 分页查询结果
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 查询委托竞价列表
 * @param params 查询参数
 * @returns Promise<PageResult<EntrustBiddingRecord>> 返回分页的委托竞价列表
 */
export const queryPageAll = (params?: QueryPageParams) => {
  return defHttp.get<PageResult<EntrustBiddingRecord>>({
    url: Api.queryPageAll,
    params,
  });
};

/**
 * 新增委托竞价
 * @param params 委托竞价信息
 * @returns Promise<any> 返回新增结果
 */
export const addEntrustBidding = (params: Partial<EntrustBiddingRecord>) => {
  return defHttp.post({
    url: Api.add,
    params,
  });
};

/**
 * 编辑委托竞价
 * @param params 委托竞价信息
 * @returns Promise<any> 返回编辑结果
 */
export const editEntrustBidding = (params: Partial<EntrustBiddingRecord> & { id: string }) => {
  return defHttp.put({
    url: Api.edit,
    params,
  });
};

/**
 * 删除单个委托竞价
 * @param params 删除参数
 * @param handleSuccess 成功回调
 */
export const deleteOne = (params: { id: string }, handleSuccess: () => void) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除委托竞价
 * @param params 删除参数
 * @param handleSuccess 成功回调
 */
export const batchDelete = (params: { ids: string[] }, handleSuccess: () => void) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 撤拍委托竞价
 * @param params 撤拍参数
 * @returns Promise<any> 返回撤拍结果
 */
export const withdrawEntrustBidding = (params: { id: string }) => {
  return defHttp.post({
    url: Api.withdraw,
    params,
  });
};

/**
 * 导出Excel
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入Excel
 */
export const getImportUrl = Api.importExcel;

/**
 * 根据ID查询委托竞价详情（用于编辑功能）
 * @param id 委托竞价记录ID
 * @returns Promise<EntrustBiddingRecord> 返回委托竞价详情
 */
export const queryOrderItemTempById = (id: string) => {
  return defHttp.get<EntrustBiddingRecord>({
    url: Api.queryOrderItemTempById,
    params: { id },
  });
};
