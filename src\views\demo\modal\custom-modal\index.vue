<template>
  <PageWrapper title="自定义弹窗组件演示">
    <div class="p-4">
      <a-card title="CustomModal 组件演示">
        <div class="demo-buttons">
          <a-button type="primary" @click="showBasicModal">基础弹窗</a-button>
          <a-button type="primary" @click="showConfirmModal">确认弹窗</a-button>
          <a-button type="primary" @click="showCustomContentModal">自定义内容弹窗</a-button>
          <a-button type="primary" @click="showNoFooterModal">无底部弹窗</a-button>
          <a-button type="primary" @click="showLargeModal">大尺寸弹窗</a-button>
        </div>

        <div class="demo-description">
          <h3>组件特性</h3>
          <ul>
            <li>✅ 固定48px高度的渐变头部</li>
            <li>✅ 左侧logo + 中间标题 + 右侧关闭按钮布局</li>
            <li>✅ 标题使用PingFang Bold字体，18px，白色</li>
            <li>✅ 头部背景渐变：rgba(0, 76, 102, 0.9) → rgba(0, 76, 102, 0.6)</li>
            <li>✅ 支持自定义内容和底部按钮</li>
            <li>✅ 完全基于Ant Design Modal封装</li>
          </ul>
        </div>
      </a-card>

      <!-- 基础弹窗 -->
      <CustomModal
        v-model:open="basicModalVisible"
        title="基础弹窗"
        @confirm="handleBasicConfirm"
        @cancel="handleBasicCancel"
      >
        <p>这是一个基础的自定义弹窗示例。</p>
        <p>头部使用了渐变背景色，左侧有logo，中间是标题，右侧是关闭按钮。</p>
        <p>底部有取消和确认按钮。</p>
      </CustomModal>

      <!-- 确认弹窗 -->
      <CustomModal
        v-model:open="confirmModalVisible"
        title="自主委托"
        width="600"
        @confirm="handleConfirmConfirm"
        @cancel="handleConfirmCancel"
      >
        <div class="confirm-content">
          <p>我方仅将客户资产信息在我网进行公示，我司不会对客户所发布信息做任何协助</p>
          <p>请确认是否选择自主服务</p>
        </div>
      </CustomModal>

      <!-- 自定义内容弹窗 -->
      <CustomModal
        v-model:open="customContentModalVisible"
        title="自定义内容弹窗"
        width="700"
        @confirm="handleCustomContentConfirm"
        @cancel="handleCustomContentCancel"
      >
        <div class="custom-content">
          <a-form :model="formData" layout="vertical">
            <a-form-item label="用户名">
              <a-input v-model:value="formData.username" placeholder="请输入用户名" />
            </a-form-item>
            <a-form-item label="邮箱">
              <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
            </a-form-item>
            <a-form-item label="描述">
              <a-textarea v-model:value="formData.description" placeholder="请输入描述" :rows="4" />
            </a-form-item>
          </a-form>
        </div>
      </CustomModal>

      <!-- 无底部弹窗 -->
      <CustomModal
        v-model:open="noFooterModalVisible"
        title="无底部弹窗"
        :show-footer="false"
        @close="handleNoFooterClose"
      >
        <div class="no-footer-content">
          <p>这是一个没有底部按钮的弹窗。</p>
          <p>只能通过右上角的关闭按钮或点击遮罩层关闭。</p>
          <div class="custom-actions">
            <a-button @click="noFooterModalVisible = false">自定义关闭</a-button>
          </div>
        </div>
      </CustomModal>

      <!-- 大尺寸弹窗 -->
      <CustomModal
        v-model:open="largeModalVisible"
        title="大尺寸弹窗"
        width="900"
        @confirm="handleLargeConfirm"
        @cancel="handleLargeCancel"
      >
        <div class="large-content">
          <h4>这是一个大尺寸的弹窗</h4>
          <p>可以容纳更多的内容，比如表格、图表等复杂组件。</p>
          
          <a-table :columns="tableColumns" :data-source="tableData" :pagination="false" size="small">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small">编辑</a-button>
                <a-button type="link" size="small" danger>删除</a-button>
              </template>
            </template>
          </a-table>
        </div>
      </CustomModal>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { PageWrapper } from '/@/components/Page'
import { CustomModal } from '/@/components/Modal'
import { useMessage } from '/@/hooks/web/useMessage'

const { createMessage } = useMessage()

// 弹窗显示状态
const basicModalVisible = ref(false)
const confirmModalVisible = ref(false)
const customContentModalVisible = ref(false)
const noFooterModalVisible = ref(false)
const largeModalVisible = ref(false)

// 表单数据
const formData = reactive({
  username: '',
  email: '',
  description: '',
})

// 表格数据
const tableColumns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '年龄', dataIndex: 'age', key: 'age' },
  { title: '地址', dataIndex: 'address', key: 'address' },
  { title: '操作', key: 'action' },
]

const tableData = [
  { key: '1', name: '张三', age: 32, address: '北京市朝阳区' },
  { key: '2', name: '李四', age: 28, address: '上海市浦东新区' },
  { key: '3', name: '王五', age: 35, address: '广州市天河区' },
]

// 弹窗显示方法
const showBasicModal = () => {
  basicModalVisible.value = true
}

const showConfirmModal = () => {
  confirmModalVisible.value = true
}

const showCustomContentModal = () => {
  customContentModalVisible.value = true
}

const showNoFooterModal = () => {
  noFooterModalVisible.value = true
}

const showLargeModal = () => {
  largeModalVisible.value = true
}

// 事件处理方法
const handleBasicConfirm = () => {
  createMessage.success('基础弹窗确认')
  basicModalVisible.value = false
}

const handleBasicCancel = () => {
  createMessage.info('基础弹窗取消')
}

const handleConfirmConfirm = () => {
  createMessage.success('确认选择自主服务')
  confirmModalVisible.value = false
}

const handleConfirmCancel = () => {
  createMessage.info('取消自主服务')
}

const handleCustomContentConfirm = () => {
  console.log('表单数据:', formData)
  createMessage.success('自定义内容弹窗确认')
  customContentModalVisible.value = false
}

const handleCustomContentCancel = () => {
  createMessage.info('自定义内容弹窗取消')
}

const handleNoFooterClose = () => {
  createMessage.info('无底部弹窗关闭')
}

const handleLargeConfirm = () => {
  createMessage.success('大尺寸弹窗确认')
  largeModalVisible.value = false
}

const handleLargeCancel = () => {
  createMessage.info('大尺寸弹窗取消')
}
</script>

<style lang="less" scoped>
.demo-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.demo-description {
  h3 {
    color: #1890ff;
    margin-bottom: 12px;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      padding: 4px 0;
      color: #666;
    }
  }
}

.confirm-content {
  text-align: center;
  padding: 20px 0;

  p {
    margin: 12px 0;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
  }
}

.custom-content {
  .ant-form {
    max-width: 100%;
  }
}

.no-footer-content {
  text-align: center;
  padding: 20px 0;

  .custom-actions {
    margin-top: 20px;
  }
}

.large-content {
  h4 {
    color: #1890ff;
    margin-bottom: 16px;
  }

  p {
    margin-bottom: 20px;
    color: #666;
  }
}

.p-4 {
  padding: 16px;
}
</style>
