<template>
  <PageWrapper title="Tiptap图片上传测试">
    <div class="p-4">
      <a-card title="图片上传功能测试">
        <div class="mb-4">
          <a-alert
            message="测试说明"
            description="点击编辑器工具栏中的图片按钮，选择图片文件进行上传测试。上传成功后图片会自动插入到编辑器中。"
            type="info"
            show-icon
          />
        </div>

        <TiptapEditor
          v-model="content"
          height="400px"
          placeholder="请点击工具栏中的图片按钮测试上传功能..."
          @change="handleChange"
        />

        <div class="mt-4">
          <h4>编辑器内容（HTML）：</h4>
          <pre class="content-html">{{ content }}</pre>
        </div>

        <div class="mt-4">
          <h4>渲染效果：</h4>
          <div class="content-preview" v-html="content"></div>
        </div>
      </a-card>

      <a-card title="上传日志" class="mt-4">
        <div class="upload-log">
          <div v-for="(log, index) in uploadLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-type" :class="log.type">{{ log.type.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="uploadLogs.length === 0" class="no-logs">
            暂无上传记录
          </div>
        </div>
      </a-card>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { PageWrapper } from '/@/components/Page'
import { TiptapEditor } from '/@/components/TiptapEditor'

const content = ref('<p>请测试图片上传功能</p>')

interface UploadLog {
  time: string
  type: 'info' | 'success' | 'error'
  message: string
}

const uploadLogs = ref<UploadLog[]>([])

const addLog = (type: UploadLog['type'], message: string) => {
  uploadLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    type,
    message,
  })
  // 只保留最近20条记录
  if (uploadLogs.value.length > 20) {
    uploadLogs.value = uploadLogs.value.slice(0, 20)
  }
}

const handleChange = (value: string) => {
  // 检测是否有新的图片被插入
  const imgMatches = value.match(/<img[^>]+src="([^"]+)"[^>]*>/g)
  if (imgMatches) {
    const currentImgCount = imgMatches.length
    const previousImgCount = content.value.match(/<img[^>]+src="([^"]+)"[^>]*>/g)?.length || 0
    
    if (currentImgCount > previousImgCount) {
      addLog('success', `检测到新图片插入，当前共有 ${currentImgCount} 张图片`)
    }
  }
  
  addLog('info', `内容已更新，长度: ${value.length} 字符`)
}

// 初始化日志
addLog('info', '编辑器初始化完成')
</script>

<style lang="less" scoped>
.content-html {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.content-preview {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  background: #fff;
  min-height: 100px;
  
  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 8px 0;
  }
  
  :deep(p) {
    margin: 8px 0;
    
    &:first-child {
      margin-top: 0;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.upload-log {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 8px;
  background: #fafafa;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
  
  &:last-child {
    border-bottom: none;
  }
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-type {
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
  margin-right: 8px;
  min-width: 60px;
  text-align: center;
  
  &.info {
    background: #e6f7ff;
    color: #1890ff;
  }
  
  &.success {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.error {
    background: #fff2f0;
    color: #ff4d4f;
  }
}

.log-message {
  flex: 1;
  color: #333;
}

.no-logs {
  text-align: center;
  color: #999;
  padding: 20px;
  font-style: italic;
}

.p-4 {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
