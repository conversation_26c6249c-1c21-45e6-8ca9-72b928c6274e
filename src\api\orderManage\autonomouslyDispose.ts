import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

/**
 * 委托处置相关API接口
 */
enum Api {
  // 查询列表
  queryPageAll = '/hgy/entrustService/hgyAssetEntrust/queryPageAll',
  // 根据ID查询详情
  queryEntrustById = '/hgy/entrustService/hgyAssetEntrust/queryEntrustById',
  // 新增
  add = '/hgy/entrustService/hgyAssetEntrust/add',
  // 编辑
  edit = '/hgy/entrustService/hgyAssetEntrust/edit',
  // 删除单个
  deleteOne = '/hgy/entrustService/hgyAssetEntrust/delete',
  // 自定义删除（委托处置和自主处置通用）
  customEntrustDelete = '/hgy/entrustService/hgyAssetEntrust/customEntrustDelete',
  // 批量删除
  deleteBatch = '/hgy/entrustService/hgyAssetEntrust/deleteBatch',
  // 撤拍
  withdraw = '/hgy/entrustService/hgyAssetEntrust/withdraw',
  // 导出
  exportXls = '/hgy/entrustService/hgyAssetEntrust/exportXls',
  // 导入
  importExcel = '/hgy/entrustService/hgyAssetEntrust/importExcel',
}

/**
 * 委托处置记录接口
 */
export interface EntrustDisposeRecord {
  hgyAssetEntrust: hgyAssetEntrust;
  hgyEntrustOrder: hgyEntrustOrder;
  hgyAttachmentList: hgyAttachmentList;
}

interface hgyAssetEntrust {
  id: number;
  tenantId: number;
  userId: number;
  entrustOrderId: number;
  assetName: string;
  assetNo: string;
  assetType: number;
  quantity: number;
  quantityFlag: string;
  actualQuantity: string;
  unit: string;
  serviceLife: number;
  depreciationDegree: number;
  currentStatus: number;
  appraisalValue: number;
  disposalPrice: number;
  disposalStartTime: string;
  disposalEndTime: string;
  province: string;
  city: string;
  district: string;
  address: string;
  paymentMethod: number;
  isTaxIncluded: number;
  specialNotes: string;
  createTime: string;
  updateTime: string;
  delFlag: number;
  createBy: string;
  updateBy: string;
  transactionStatus: string;
  status: number;
  provinceCode: string;
  cityCode: string;
  districtCode: string;
  servicePayType: number;
  transactionPrice: number;
  premiumAmount: number;
  premiumRate: number;
  coverImage: string;
  auctionModel: number;
}

interface hgyEntrustOrder {
  id: string;
  tenantId: number;
  userId: string;
  entrustCompanyId: number;
  entrustCompanyName: string;
  onEntrustCompanyId: number;
  onEntrustCompanyName: string;
  entrustType: number;
  serviceType: number;
  status: number;
  relationUser: string;
  relationPhone: string;
  auditOpinion: string;
  auditTime: string;
  auditUser: string;
  createTime: string;
  updateTime: string;
  delFlag: number;
  createBy: string;
  updateBy: string;
}

interface hgyAttachmentList {
  id: string;
  tenantId: number;
  userId: string;
  bizType: string;
  bizId: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  createTime: string;
  delFlag: number;
  createBy: string;
  updateBy: string;
  updateTime: string;
}

/**
 * 分页查询参数
 */
export interface QueryPageParams {
  pageNo?: number;
  pageSize?: number;
  /* entrustOrderId?: string;
  assetName?: string;
  entrustCompanyName?: string;
  status?: string;
  [key: string]: any; */
  itemName?: string; // 标的名称
  status?: string; // 状态
  auctionDateEnd?: string; // 拍卖结束时间
  entrustStatus?: string; // 拍卖开始时间
}

/**
 * 分页查询结果
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 查询委托处置列表
 * @param params 查询参数
 * @returns Promise<PageResult<EntrustDisposeRecord>> 返回分页的委托处置列表
 */
export const queryPageAll = (params?: QueryPageParams) => {
  return defHttp.post<PageResult<EntrustDisposeRecord>>({
    url: Api.queryPageAll,
    params,
  });
};

/**
 * 根据ID查询委托详情
 * @param id 委托ID
 * @returns Promise<EntrustDisposeRecord> 返回委托详情
 */
export const queryEntrustById = (id: string) => {
  return defHttp.get<EntrustDisposeRecord>({
    url: Api.queryEntrustById,
    params: { id },
  });
};

/**
 * 新增委托处置
 * @param params 委托处置信息
 * @returns Promise<any> 返回新增结果
 */
export const addEntrustDispose = (params: Partial<EntrustDisposeRecord>) => {
  return defHttp.post({
    url: Api.add,
    params,
  });
};

/**
 * 编辑委托处置
 * @param params 委托处置信息
 * @returns Promise<any> 返回编辑结果
 */
export const editEntrustDispose = (params: Partial<EntrustDisposeRecord> & { id: string }) => {
  return defHttp.put({
    url: Api.edit,
    params,
  });
};

/**
 * 删除单个委托处置
 * @param params 删除参数
 * @param handleSuccess 成功回调
 */
export const deleteOne = (params: { id: string }, handleSuccess: () => void) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 自定义删除委托处置（委托处置和自主处置通用）
 * @param id 删除项的ID
 * @returns Promise<any> 返回删除结果
 */
export const customEntrustDelete = (id: string) => {
  return defHttp.delete({
    url: `${Api.customEntrustDelete}?id=${id}`,
  });
};

/**
 * 批量删除委托处置
 * @param params 删除参数
 * @param handleSuccess 成功回调
 */
export const batchDelete = (params: { ids: string[] }, handleSuccess: () => void) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 撤拍委托处置
 * @param params 撤拍参数
 * @returns Promise<any> 返回撤拍结果
 */
export const withdrawEntrustDispose = (params: { id: string }) => {
  return defHttp.post({
    url: Api.withdraw,
    params,
  });
};

/**
 * 导出Excel
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入Excel
 */
export const getImportUrl = Api.importExcel;
