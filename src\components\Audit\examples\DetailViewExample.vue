<template>
  <div class="detail-view-example">
    <h2>详情查看功能使用示例</h2>

    <!-- 示例按钮 -->
    <div class="example-buttons">
      <a-space wrap>
        <a-button type="primary" @click="showDetail(1, 1)"> 查看增值委托-竞价委托详情 </a-button>
        <a-button type="primary" @click="showDetail(1, 2)"> 查看增值委托-资产处置详情 </a-button>
        <a-button type="primary" @click="showDetail(1, 3)"> 查看增值委托-采购信息详情 </a-button>
        <a-button type="primary" @click="showDetail(2, 1)"> 查看自主委托-竞价委托详情 </a-button>
        <a-button type="primary" @click="showDetail(2, 2)"> 查看自主委托-资产处置详情 </a-button>
        <a-button type="primary" @click="showDetail(2, 3)"> 查看自主委托-采购信息详情 </a-button>
      </a-space>
    </div>

    <!-- 详情查看弹窗 -->
    <DetailViewModal
      v-model:open="detailVisible"
      :record="currentRecord"
      :entrust-type="currentEntrustType"
      :service-type="currentServiceType"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { DetailViewModal } from '/@/components/Audit';
  import type { AuditRecord } from '/@/components/Audit/types';

  // 弹窗状态
  const detailVisible = ref(false);
  const currentRecord = ref<AuditRecord | null>(null);
  const currentEntrustType = ref(1);
  const currentServiceType = ref(1);

  // 模拟数据
  const mockRecord: AuditRecord = {
    id: '123456789',
    entrustType: 1,
    serviceType: 1,
    title: '测试委托单',
    status: 1,
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString(),
  };

  // 显示详情
  function showDetail(entrustType: number, serviceType: number) {
    currentRecord.value = {
      ...mockRecord,
      entrustType,
      serviceType,
    };
    currentEntrustType.value = entrustType;
    currentServiceType.value = serviceType;
    detailVisible.value = true;
  }

  // 关闭详情弹窗
  function handleDetailClose() {
    detailVisible.value = false;
    currentRecord.value = null;
  }
</script>

<style lang="less" scoped>
  .detail-view-example {
    padding: 24px;

    h2 {
      font-family: 'PingFang Bold', sans-serif;
      color: #333;
      margin-bottom: 24px;
    }

    .example-buttons {
      margin-bottom: 24px;
    }
  }

  // 详情查看弹窗内容样式优化
  :deep(.custom-modal-content) {
    // 审核组件内部的内容区域样式调整
    .audit-detail-container {
      padding: 0;
    }
  }
</style>
