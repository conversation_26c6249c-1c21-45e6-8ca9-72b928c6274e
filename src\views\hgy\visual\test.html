<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据可视化大屏布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #0a0e27;
            color: #ffffff;
            font-family: Arial, sans-serif;
        }
        
        .visual-container {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }
        
        .visual-content {
            width: 1920px;
            height: 1080px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            transform-origin: center center;
        }
        
        .visual-header {
            height: 120px;
            background: linear-gradient(135deg, #12e6db 0%, #097884 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }
        
        .visual-middle {
            display: flex;
            height: 627px;
            padding: 20px;
            gap: 20px;
        }
        
        .visual-left {
            flex: 0 0 666px;
            height: 627px;
            background: linear-gradient(135deg, rgba(18, 230, 219, 0.2) 0%, rgba(18, 230, 219, 0.1) 100%);
            border: 1px solid rgba(18, 230, 219, 0.3);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .visual-center {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
            height: 627px;
        }
        
        .center-top {
            height: 200px;
            background: linear-gradient(135deg, rgba(18, 230, 219, 0.2) 0%, rgba(18, 230, 219, 0.1) 100%);
            border: 1px solid rgba(18, 230, 219, 0.3);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .center-bottom {
            flex: 1;
            background: linear-gradient(135deg, rgba(18, 230, 219, 0.2) 0%, rgba(18, 230, 219, 0.1) 100%);
            border: 1px solid rgba(18, 230, 219, 0.3);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .visual-right {
            flex: 0 0 666px;
            height: 627px;
            background: linear-gradient(135deg, rgba(18, 230, 219, 0.2) 0%, rgba(18, 230, 219, 0.1) 100%);
            border: 1px solid rgba(18, 230, 219, 0.3);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .visual-footer {
            height: 333px;
            padding: 20px;
            display: flex;
            gap: 20px;
        }
        
        .chart-card {
            flex: 1;
            background: linear-gradient(135deg, rgba(18, 230, 219, 0.2) 0%, rgba(18, 230, 219, 0.1) 100%);
            border: 1px solid rgba(18, 230, 219, 0.3);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .size-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.5);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="visual-container">
        <div class="visual-content">
            <!-- 头部区域 -->
            <div class="visual-header">
                <div class="size-info">1920×120</div>
                头部区域 - 标题、导航按钮、时间
            </div>
            
            <!-- 中间内容区域 -->
            <div class="visual-middle">
                <!-- 左侧：标的数据排名 -->
                <div class="visual-left">
                    <div class="size-info">666×627</div>
                    标的数据排名
                </div>
                
                <!-- 中间：数据指标和小卡片 -->
                <div class="visual-center">
                    <!-- 上部：成交总额、溢价总额、总溢价率 -->
                    <div class="center-top">
                        <div class="size-info">588×200</div>
                        主要KPI指标
                    </div>
                    <!-- 下部：10个小卡片 -->
                    <div class="center-bottom">
                        <div class="size-info">588×407</div>
                        10个小卡片（左右各5个）
                    </div>
                </div>
                
                <!-- 右侧：资产处置数据排名 -->
                <div class="visual-right">
                    <div class="size-info">666×627</div>
                    资产处置数据排名
                </div>
            </div>
            
            <!-- 底部区域：三个图表卡片 -->
            <div class="visual-footer">
                <div class="chart-card">
                    <div class="size-info">626×293</div>
                    成交额排名（奖牌式）
                </div>
                <div class="chart-card">
                    <div class="size-info">626×293</div>
                    标的溢价趋势图
                </div>
                <div class="chart-card">
                    <div class="size-info">626×293</div>
                    资产处置溢价趋势图
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 响应式适配
        function handleResize() {
            const designWidth = 1920;
            const designHeight = 1080;
            const currentWidth = window.innerWidth;
            const currentHeight = window.innerHeight;
            
            const scaleX = currentWidth / designWidth;
            const scaleY = currentHeight / designHeight;
            const scale = Math.min(scaleX, scaleY);
            
            const container = document.querySelector('.visual-content');
            if (container) {
                container.style.transform = `scale(${scale})`;
            }
        }
        
        window.addEventListener('resize', handleResize);
        handleResize();
    </script>
</body>
</html>
