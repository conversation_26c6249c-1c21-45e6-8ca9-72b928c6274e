<template>
  <div class="step-panel">
    <!-- 使用 Ant Design 表单组件包裹整个表单 -->
    <a-form
      ref="formRef"
      layout="horizontal"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: 4, style: { textAlign: 'left' } }"
      :wrapper-col="{ span: 20 }"
    >
      <!-- 联系人信息 -->
      <div class="form-section">
        <h3 class="section-title">联系人信息</h3>
        <div class="form-row">
          <a-form-item label="联系人姓名" :name="['contactInfo', 'contactName']" required class="form-item-half">
            <a-input v-model:value="formData.contactInfo.contactName" placeholder="请输入联系人姓名" size="large" />
          </a-form-item>
          <a-form-item label="联系电话" :name="['contactInfo', 'contactPhone']" required class="form-item-half">
            <a-input v-model:value="formData.contactInfo.contactPhone" placeholder="请输入联系电话" maxlength="11" size="large" />
          </a-form-item>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import type { FormInstance } from 'ant-design-vue';

  // 定义 Props
  interface Props {
    modelValue: {
      contactInfo: {
        contactName: string;
        contactPhone: string;
      };
    };
  }

  const props = defineProps<Props>();

  // 定义 Emits
  interface Emits {
    (e: 'update:modelValue', value: Props['modelValue']): void;
  }

  const emit = defineEmits<Emits>();

  // 表单引用
  const formRef = ref<FormInstance>();

  // 计算属性：表单数据
  const formData = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  // 表单校验规则
  const formRules = {
    contactInfo: {
      contactName: [
        { required: true, message: '请输入联系人姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '联系人姓名长度应在2-20个字符之间', trigger: 'blur' },
      ],
      contactPhone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
      ],
    },
  };

  // 表单验证方法
  const validateForm = async (): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  };

  // 清除表单验证
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  // 暴露方法给父组件
  defineExpose({
    validateForm,
    clearValidate,
  });
</script>

<style scoped>
  .step-panel {
    background: #fff;
    border-radius: 8px;
  }

  .form-section {
    margin-bottom: 32px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  /* 纯CSS布局样式 */
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
    gap: 16px;
  }

  .form-item-full {
    flex: 1;
    min-width: 100%;
  }

  .form-item-half {
    flex: 1;
    min-width: calc(50% - 8px);
  }

  /* 确保label和输入框之间有10px间距 */
  :deep(.ant-form-item-label) {
    margin-right: 10px;
  }

  :deep(.ant-form-item-label > label) {
    margin-right: 0;
  }

  /* 响应式布局 */
  @media (max-width: 768px) {
    .form-item-half {
      min-width: 100%;
    }
  }
</style>
