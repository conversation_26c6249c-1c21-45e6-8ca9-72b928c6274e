<template>
  <div class="custom-steps">
    <div 
      v-for="(step, index) in steps" 
      :key="index"
      class="step-item"
      :class="{
        'step-active': index === current,
        'step-completed': index < current,
        'step-disabled': index > current,
        'step-clickable': index < current
      }"
      :style="`z-index: ${steps.length - index}`"
      @click="handleStepClick(index)"
    >
      <!-- 步骤内容 -->
      <div class="step-content">
        <span class="step-title">{{ step.title }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

// 步骤项接口定义
interface StepItem {
  title: string; // 步骤标题
  description?: string; // 步骤描述（可选）
}

export default defineComponent({
  name: 'CustomSteps',
  props: {
    // 步骤数据数组
    steps: {
      type: Array as PropType<StepItem[]>,
      required: true,
      default: () => []
    },
    // 当前激活的步骤索引
    current: {
      type: Number,
      default: 0
    }
  },
  emits: ['change'],
  setup(props, { emit }) {
    // 处理步骤点击
    const handleStepClick = (index: number) => {
      // 只允许点击已完成的步骤（当前步骤之前的步骤）
      if (index < props.current) {
        emit('change', index);
      }
    };

    return {
      handleStepClick
    };
  }
});
</script>

<style lang="less" scoped>
.custom-steps {
  display: flex;
  align-items: center;
  width: 100%; // 宽度占满容器
  height: 53px; // 调整为53px高度
}

.step-item {
  position: relative;
  display: flex;
  align-items: center;
  height: 53px; // 调整为53px高度
  flex: 1; // 平均分配宽度，占满容器
  margin-right: -18px; // 创建步骤条重叠效果，重叠宽度与箭头宽度一致
  
  &:last-child {
    margin-right: 0; // 最后一个步骤不需要重叠
  }
  
  &:first-child {
    .step-content {
      padding-left: 30px; // 第一个步骤左侧内边距
      border-left: none; // 第一个步骤左侧不需要凹槽
      clip-path: polygon(0 0, calc(100% - 24px) 0, 100% 50%, calc(100% - 24px) 100%, 0 100%); // 右侧箭头形状
    }
  }
  
  &:last-child {
    .step-content {
      padding-right: 30px; // 最后一个步骤右侧内边距
      padding-left: 30px; // 最后一个步骤左侧内边距
      clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 24px 50%); // 左侧箭头形状，右侧为直边
    }
  }
  
  &:not(:first-child):not(:last-child) {
    .step-content {
      clip-path: polygon(0 0, calc(100% - 24px) 0, 100% 50%, calc(100% - 24px) 100%, 0 100%, 24px 50%); // 左侧箭头+右侧箭头
    }
  }
}

.step-content {
  position: relative;
  background: #f2f2f2; // 默认背景色（未激活状态）
  display: flex;
  align-items: center;
  justify-content: center;
  color: #004C66; // 默认文字颜色
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
  height: 53px; // 调整为53px高度
  line-height: 21px; // 调整行高适应新高度
  transition: all 0.3s ease; // 添加过渡动画
  width: 100%; // 宽度占满
}

// 激活状态样式（使用用户指定的#004C66颜色）
.step-active {
  .step-content {
    background: #004C66; // 用户指定的当前步骤背景色
    color: #fff; // 白色文字
  }
}

// 已完成状态样式
.step-completed {
  .step-content {
    background: #f2f2f2; // 已完成状态背景色
    color: #004C66; // 已完成状态文字颜色
  }
}

// 可点击状态样式（已完成的步骤）
.step-clickable {
  cursor: pointer;
  
  .step-content {
    &:hover {
      background: #e6f7ff; // 悬停时的背景色
      color: #004C66;
    }
  }
}

// 禁用状态样式
.step-disabled {
  .step-content {
    background: #f2f2f2; // 禁用状态背景色
    color: #004C66; // 禁用状态文字颜色
    cursor: not-allowed;
  }
}

// 响应式设计 - 中等屏幕调整
@media (max-width: 1200px) {
  .step-item {
    margin-right: -20px; // 中等屏幕下调整重叠宽度与箭头宽度一致
    
    &:last-child {
      margin-right: 0; // 最后一个步骤不需要重叠
    }
    &:first-child {
      .step-content {
        clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%); // 调整箭头大小
      }
    }
    
    &:last-child {
      .step-content {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20px 50%); // 左侧箭头形状，右侧为直边
      }
    }
    
    &:not(:first-child):not(:last-child) {
      .step-content {
        clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%, 20px 50%); // 左侧箭头+右侧箭头
      }
    }
  }
}

// 响应式设计 - 小屏幕调整
@media (max-width: 992px) {
  .step-item {
    margin-right: -16px; // 小屏幕下调整重叠宽度与箭头宽度一致
    
    &:last-child {
      margin-right: 0; // 最后一个步骤不需要重叠
    }
    &:first-child {
      .step-content {
        clip-path: polygon(0 0, calc(100% - 16px) 0, 100% 50%, calc(100% - 16px) 100%, 0 100%); // 进一步调整箭头大小
      }
    }
    
    &:last-child {
      .step-content {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 16px 50%); // 左侧箭头形状，右侧为直边
      }
    }
    
    &:not(:first-child):not(:last-child) {
      .step-content {
        clip-path: polygon(0 0, calc(100% - 16px) 0, 100% 50%, calc(100% - 16px) 100%, 0 100%, 16px 50%); // 左侧箭头+右侧箭头
      }
    }
  }
}

// 响应式设计 - 移动端
@media (max-width: 768px) {
  .custom-steps {
    flex-direction: column; // 小屏幕下垂直排列
    align-items: stretch;
    height: auto;
  }
  
  .step-item {
    margin-bottom: 10px;
    height: auto;
    flex: none; // 移动端取消flex布局
    
    &:last-child {
      margin-bottom: 0;
    }
    
    // 移动端重置所有clip-path，使用简单的矩形
    &:first-child,
    &:last-child,
    &:not(:first-child):not(:last-child) {
      .step-content {
        clip-path: none; // 移动端移除箭头效果
      }
    }
  }
  
  .step-content {
    text-align: center;
    padding: 12px 20px;
    height: auto;
    margin-right: 0; // 移动端重置间距
  }
}
</style>