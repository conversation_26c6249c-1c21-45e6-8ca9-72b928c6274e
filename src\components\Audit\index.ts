import { withInstall } from '/@/utils';
import commonAuditModal from './src/CommonAuditModal.vue';
import commonAppreciationAudit from './src/CommonAppreciationAudit.vue';
import commonSelfAudit from './src/CommonSelfAudit.vue';
import commonAuctionItemsModal from './src/CommonAuctionItemsModal.vue';
import detailViewModal from './src/DetailViewModal.vue';

// 使用 withInstall 包装组件，支持全局注册
export const CommonAuditModal = withInstall(commonAuditModal);
export const CommonAppreciationAudit = withInstall(commonAppreciationAudit);
export const CommonSelfAudit = withInstall(commonSelfAudit);
export const CommonAuctionItemsModal = withInstall(commonAuctionItemsModal);
export const DetailViewModal = withInstall(detailViewModal);

// 导出类型定义
export * from './types';

// 默认导出主要组件
export default CommonAuditModal;
