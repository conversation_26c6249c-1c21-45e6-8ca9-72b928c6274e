@menu-prefix-cls: ~'@{namespace}-menu';
@menu-popup-prefix-cls: ~'@{namespace}-menu-popup';
@submenu-popup-prefix-cls: ~'@{namespace}-menu-submenu-popup';

@transition-time: 0.2s;
@menu-dark-subsidiary-color: rgba(255, 255, 255, 0.7);

.light-border {
  &::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    display: block;
    width: 2px;
    background-color: @primary-color;
    content: '';
  }
}

.@{menu-prefix-cls}-menu-popover {
  .ant-popover-arrow {
    display: none;
  }

  .ant-popover-inner-content {
    padding: 0;
  }

  .@{menu-prefix-cls} {
    &-opened > * > &-submenu-title-icon {
      transform: translateY(-50%) rotate(90deg) !important;
    }

    &-item,
    &-submenu-title {
      position: relative;
      z-index: 1;
      padding: 10px 14px;
      color: @menu-dark-subsidiary-color;
      cursor: pointer;
      transition: all @transition-time @ease-in-out;

      &-icon {
        position: absolute;
        top: 50%;
        right: 18px;
        transform: translateY(-50%) rotate(-90deg);
        transition: transform @transition-time @ease-in-out;
      }
    }

    &-dark {
      .@{menu-prefix-cls}-item,
      .@{menu-prefix-cls}-submenu-title {
        color: @menu-dark-subsidiary-color;
        // background: @menu-dark-active-bg;

        &:hover {
          color: #fff;
        }

        &-selected {
          color: #fff;
          background-color: @primary-color !important;
        }
      }
      // 彩色模式(绿色，橘红等)
      &.bright {
        .@{menu-prefix-cls}-item,
        .@{menu-prefix-cls}-submenu-title {
          color: #fff;
          &:hover {
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }

    &-light {
      .@{menu-prefix-cls}-item,
      .@{menu-prefix-cls}-submenu-title {
        color: @text-color-base;

        &:hover {
          color: @primary-color;
        }

        &-selected {
          z-index: 2;
          color: @primary-color;
          background-color: fade(@primary-color, 10);

          .light-border();
        }
      }
    }
  }
}

.content();
.content() {
  .@{menu-prefix-cls} {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
    font-size: @font-size-base;
    color: @text-color-base;
    list-style: none;
    outline: none;

    // .collapse-transition {
    //   transition: @transition-time height ease-in-out, @transition-time padding-top ease-in-out,
    //     @transition-time padding-bottom ease-in-out;
    // }

    &-light {
      background-color: #fff;
      color: rgba(0, 0, 0, 0.65);
      .@{menu-prefix-cls} {
        color: rgba(0, 0, 0, 0.65);
      }
      .@{namespace}-menu-submenu:not(.@{namespace}-menu-item-active) .@{namespace}-menu-submenu-title {
        .anticon {
          color: rgba(0, 0, 0, 0.9);
        }
      }
      .@{menu-prefix-cls}-submenu-active {
        color: @primary-color !important;

        &-border {
          .light-border();
        }
      }
    }

    &-dark {
      .@{menu-prefix-cls}-submenu-active {
        color: #fff !important;
      }
    }

    &-item {
      position: relative;
      z-index: 1;
      display: flex;
      font-size: @font-size-base;
      list-style: none;
      cursor: pointer;
      outline: none;
      align-items: center;

      &:hover,
      &:active {
        color: inherit;
      }
    }

    &-item > i {
      margin-right: 6px;
    }

    &-submenu-title > i,
    &-submenu-title span > i {
      margin-right: 8px;
    }

    // vertical
    &-vertical &-item,
    &-vertical &-submenu-title {
      position: relative;
      z-index: 1;
      padding: 14px 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      font-size: 18px; // 默认父菜单字体大小

      &:hover {
        color: @primary-color;
      }

      .@{menu-prefix-cls}-tooltip {
        width: calc(100% - 0px);
        padding: 12px 0;
        text-align: center;
      }
      .@{menu-prefix-cls}-submenu-popup {
        padding: 12px 0;
      }
    }

    // 子菜单项特殊样式
    &-vertical &-submenu &-item {
      font-size: 16px; // 子菜单字体大小

      // 隐藏子菜单图标
      .anticon {
        display: none !important;
      }
    }

    &-vertical &-submenu-collapse {
      .@{submenu-popup-prefix-cls} {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .@{menu-prefix-cls}-submenu-collapsed-show-tit {
        flex-direction: column;
      }
    }

    &-vertical&-collapse &-item,
    &-vertical&-collapse &-submenu-title {
      padding: 0 0;
    }

    &-vertical &-submenu-title-icon {
      position: absolute;
      top: 50%;
      right: 18px;
      transform: translateY(-50%);
    }

    &-submenu-title-icon {
      transition: transform @transition-time @ease-in-out;
    }

    &-vertical &-opened > * > &-submenu-title-icon {
      transform: translateY(-50%) rotate(180deg);
    }

    &-vertical &-submenu {
      &-nested {
        padding-left: 20px;
      }
      .@{menu-prefix-cls}-item {
        padding-left: 43px;
      }
    }

    &-light&-vertical &-item {
      &-active:not(.@{menu-prefix-cls}-submenu) {
        z-index: 2;
        color: @primary-color;
        background-color: fade(@primary-color, 10);

        .light-border();
      }
      &-active.@{menu-prefix-cls}-submenu {
        color: @primary-color;
      }
    }

    &-light&-vertical&-collapse {
      > li.@{menu-prefix-cls}-item-active,
      .@{menu-prefix-cls}-submenu-active {
        position: relative;
        background-color: fade(@primary-color, 5);

        &::after {
          display: none;
        }

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          width: 3px;
          height: 100%;
          background-color: @primary-color;
          content: '';
        }
      }
    }

    &-dark&-vertical &-item,
    &-dark&-vertical &-submenu-title {
      color: #ccc; // 未选中状态文字颜色
      padding-left: 46px !important;
      font-size: 18px; // 父菜单文字大小
      padding: 12.5px 0 12.5px 46px !important;
      z-index: 101;
      background: transparent !important;

      &-active:not(.@{menu-prefix-cls}-submenu) {
        color: #fff !important;
        /* background-color: #4d8294 !important;
        width: 202px;
        height: 42px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-left: calc(50% - 101px); // 居中显示：(容器宽度 - 202px) / 2
        position: relative; */
      }

      &:hover {
        color: #fff;
        /* background-color: #4d8294; // hover状态与选中状态保持一致
        display: flex;
        align-items: center;
        justify-content: flex-start;
        // margin-left: calc(50% - 101px); // 居中显示：(容器宽度 - 202px) / 2
        position: relative; */
      }
    }
    // update-begin--author:liaozhiyang---date:20240408---for：【QQYUN-8922】左侧导航栏文字颜色调整区分彩色和暗黑
    &-dark&-vertical&.bright &-item,
    &-dark&-vertical.bright &-submenu-title {
      color: rgba(255, 255, 255, 1);
      &-active:not(.@{menu-prefix-cls}-submenu) {
        color: #fff !important;
        background-color: @primary-color !important;
      }

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
    // update-end--author:liaozhiyang---date:20240408---for：【QQYUN-8922】左侧导航栏文字颜色调整区分彩色和暗黑

    &-dark&-vertical&-collapse {
      // 折叠状态下移除左内边距，确保菜单项居中显示
      .@{menu-prefix-cls}-item,
      .@{menu-prefix-cls}-submenu-title {
        padding-left: 0 !important;
        padding-right: 0 !important;
      }

      > li.@{menu-prefix-cls}-item-active,
      .@{menu-prefix-cls}-submenu-active {
        position: relative;
        color: #fff !important;
        background-color: @primary-color !important;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          width: 3px;
          height: 100%;
          background-color: @primary-color;
          content: '';
        }

        .@{menu-prefix-cls}-submenu-collapse {
          background-color: transparent;
        }
      }
    }

    // 二级子菜单样式（通用样式）
    &-dark&-vertical > &-submenu > ul > &-item {
      padding-left: 84px !important; // 子菜单左边距84px
      padding-right: 20px !important;
      font-size: 16px; // 子菜单文字大小16px
      color: #ccc; // 子菜单未选中状态文字颜色
      margin-left: calc(50% - 101px); // 居中显示：(容器宽度 - 202px) / 2
      background-color: #fff;

      span {
        margin-left: -20px !important;
      }

      // 隐藏子菜单图标
      .anticon {
        display: none !important;
      }

      &:hover {
        color: #fff;
      }
    }

    // 二级子菜单样式（无子项的菜单项 - 应用特殊选中样式）
    &-dark&-vertical > &-submenu > ul > li:not(.@{menu-prefix-cls}-submenu).@{menu-prefix-cls}-item {
      &-active,
      &-active:hover {
        margin-top: 2px;
        color: #fff;
        border-radius: 8px;
        margin-right: 30px;
        background-color: #4d8294 !important;
        /* width: 202px;
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: flex-start; */
      }
    }

    // 二级子菜单样式（有子项的菜单项 - 调整缩进位置）
    &-dark&-vertical > &-submenu > ul > li.@{menu-prefix-cls}-submenu {
      .@{menu-prefix-cls}-submenu-title {
        padding-left: 84px !important; // 与没有子项的菜单项保持一致的左边距
        padding-right: 20px !important;
        font-size: 16px; // 保持与二级菜单一致的字体大小
        color: #ccc;
        margin-left: calc(50% - 101px); // 居中显示：(容器宽度 - 202px) / 2

        span {
          margin-left: 0.5rem !important;
        }

        &.@{menu-prefix-cls}-submenu-active {
          color: #fff;
          background-color: transparent !important; // 有子项的菜单选中时不显示背景色
          border-radius: 0;
          margin-right: 0;
          margin-top: 0;
        }

        &:hover {
          color: #fff;
        }
      }
    }

    // 三级及以上子菜单样式（增加缩进）
    &-dark&-vertical &-submenu &-submenu &-item {
      padding-left: 134px !important; // 三级子菜单左边距增加20px
      padding-right: 20px !important;
      font-size: 14px !important; // 三级子菜单文字大小14px
      color: #ccc;
      margin-left: calc(50% - 41px); // 调整居中位置以适应增加的缩进

      span {
        margin-left: -80px !important;
      }

      // 隐藏子菜单图标
      .anticon {
        display: none !important;
      }

      &-active,
      &-active:hover {
        color: #fff;
        background-color: #4d8294 !important;
        border-radius: 8px;
        margin-right: 30px;
        margin-top: 2px;
        width: 100px;
      }

      &:hover {
        color: #fff;
      }
    }

    // 三级子菜单标题样式（有子项的三级菜单）
    &-dark&-vertical &-submenu &-submenu &-submenu-title {
      padding-left: 134px !important;
      padding-right: 20px !important;
      font-size: 14px !important;
      color: #ccc;
      margin-left: 0 !important;

      span {
        // margin-left: -20px !important;
      }

      &:hover {
        color: #fff;
      }
    }

    // 四级及以上子菜单样式
    &-dark&-vertical &-submenu &-submenu &-submenu &-item {
      padding-left: 144px !important; // 四级子菜单左边距再增加20px
      font-size: 13px; // 四级子菜单文字大小13px
    }

    &-dark&-vertical &-child-item-active > &-submenu-title {
      color: #fff;

      .anticon {
        color: #fff !important;
      }
    }

    &-dark&-vertical &-opened {
      .@{menu-prefix-cls}-submenu-has-parent-submenu {
        .@{menu-prefix-cls}-submenu-title {
          background-color: transparent;
        }
      }
    }
  }
}
