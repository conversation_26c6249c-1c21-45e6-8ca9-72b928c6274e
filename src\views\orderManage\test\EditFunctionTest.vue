<template>
  <div class="edit-function-test">
    <h2>编辑功能测试页面</h2>
    
    <div class="test-section">
      <h3>委托竞价编辑测试</h3>
      <a-button type="primary" @click="testEntrustBiddingEdit">
        测试委托竞价编辑
      </a-button>
      <p>点击后会调用 queryOrderItemTempById 接口并跳转到增值委托页面</p>
    </div>

    <div class="test-section">
      <h3>自主竞价编辑测试</h3>
      <a-button type="primary" @click="testAutonomousBiddingEdit">
        测试自主竞价编辑
      </a-button>
      <p>点击后会调用 queryOrderAuctionItemById 接口并跳转到自主委托页面</p>
    </div>

    <div class="test-section">
      <h3>API 接口测试</h3>
      <a-space>
        <a-button @click="testEntrustBiddingAPI">测试委托竞价API</a-button>
        <a-button @click="testAutonomousBiddingAPI">测试自主竞价API</a-button>
      </a-space>
    </div>

    <div v-if="testResult" class="test-result">
      <h4>测试结果：</h4>
      <pre>{{ testResult }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import { queryOrderItemTempById } from '/@/api/orderManage/entrustBidding';
  import { queryOrderAuctionItemById } from '/@/api/orderManage/autonomouslyBidding';

  const router = useRouter();
  const testResult = ref('');

  // 模拟委托竞价记录
  const mockEntrustBiddingRecord = {
    id: 'test-entrust-001',
    entrustOrderId: 'ENT202401001',
    projectName: '测试委托竞价项目',
    serviceType: 1,
  };

  // 模拟自主竞价记录
  const mockAutonomousBiddingRecord = {
    id: 'test-autonomous-001',
    entrustOrderId: 'AUT202401001',
    projectName: '测试自主竞价项目',
    serviceType: 1,
  };

  // 测试委托竞价编辑功能
  async function testEntrustBiddingEdit() {
    try {
      testResult.value = '正在测试委托竞价编辑功能...';
      
      // 调用查询接口
      await queryOrderItemTempById(mockEntrustBiddingRecord.id);
      
      // 跳转到增值委托页面
      router.push({
        path: '/entrust/appreciationEntrust',
        query: {
          id: mockEntrustBiddingRecord.entrustOrderId,
          serviceType: 1,
        },
      });
      
      message.success('委托竞价编辑功能测试成功');
      testResult.value = '委托竞价编辑功能测试成功，已跳转到增值委托页面';
    } catch (error) {
      console.error('委托竞价编辑测试失败:', error);
      message.error('委托竞价编辑测试失败');
      testResult.value = `委托竞价编辑测试失败: ${error}`;
    }
  }

  // 测试自主竞价编辑功能
  async function testAutonomousBiddingEdit() {
    try {
      testResult.value = '正在测试自主竞价编辑功能...';
      
      // 调用查询接口
      await queryOrderAuctionItemById(mockAutonomousBiddingRecord.id);
      
      // 跳转到自主委托页面
      router.push({
        path: '/entrust/selfEntrust',
        query: {
          id: mockAutonomousBiddingRecord.entrustOrderId,
          serviceType: 1,
        },
      });
      
      message.success('自主竞价编辑功能测试成功');
      testResult.value = '自主竞价编辑功能测试成功，已跳转到自主委托页面';
    } catch (error) {
      console.error('自主竞价编辑测试失败:', error);
      message.error('自主竞价编辑测试失败');
      testResult.value = `自主竞价编辑测试失败: ${error}`;
    }
  }

  // 测试委托竞价API
  async function testEntrustBiddingAPI() {
    try {
      testResult.value = '正在测试委托竞价API...';
      const result = await queryOrderItemTempById(mockEntrustBiddingRecord.id);
      testResult.value = `委托竞价API测试成功:\n${JSON.stringify(result, null, 2)}`;
      message.success('委托竞价API测试成功');
    } catch (error) {
      console.error('委托竞价API测试失败:', error);
      testResult.value = `委托竞价API测试失败: ${error}`;
      message.error('委托竞价API测试失败');
    }
  }

  // 测试自主竞价API
  async function testAutonomousBiddingAPI() {
    try {
      testResult.value = '正在测试自主竞价API...';
      const result = await queryOrderAuctionItemById(mockAutonomousBiddingRecord.id);
      testResult.value = `自主竞价API测试成功:\n${JSON.stringify(result, null, 2)}`;
      message.success('自主竞价API测试成功');
    } catch (error) {
      console.error('自主竞价API测试失败:', error);
      testResult.value = `自主竞价API测试失败: ${error}`;
      message.error('自主竞价API测试失败');
    }
  }
</script>

<style lang="less" scoped>
  .edit-function-test {
    padding: 20px;

    h2 {
      color: #333;
      margin-bottom: 20px;
    }

    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      background: #fafafa;

      h3 {
        color: #666;
        margin-bottom: 15px;
      }

      p {
        color: #999;
        margin-top: 10px;
        font-size: 14px;
      }
    }

    .test-result {
      margin-top: 20px;
      padding: 15px;
      background: #f0f0f0;
      border-radius: 6px;

      h4 {
        color: #333;
        margin-bottom: 10px;
      }

      pre {
        background: #fff;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #ddd;
        font-size: 12px;
        color: #333;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
</style>
