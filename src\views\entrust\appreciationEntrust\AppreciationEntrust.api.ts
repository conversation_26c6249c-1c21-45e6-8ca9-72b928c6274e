import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  // 委托相关接口
  addDraft = '/jeecg-boot/hgy/entrustService/hgyAssetEntrust/addDraft',
  getDictItems = '/sys/dict/getDictItems/',

  // 地理位置相关
  getCurrentLocation = '/entrust/location/current',
  getAddressByCoordinates = '/entrust/location/address',

  // 文件上传相关
  uploadFile = '/sys/common/upload',

  // 数据字典相关
  getMeasurementUnits = '/sys/dict/getDictItems/measurement_unit',
  getEntrustTypes = '/sys/dict/getDictItems/entrust_type',
  getAssetTypes = '/sys/dict/getDictItems/asset_type',
  getDepreciationDegrees = '/sys/dict/getDictItems/depreciation_degree',
  getCurrentStatuses = '/sys/dict/getDictItems/current_status',
  getPaymentMethods = '/sys/dict/getDictItems/payment_method',
}

// 附件接口
export interface AttachmentItem {
  /*附件ID */
  id?: string;
  /*租户ID */
  tenantId?: number;
  /*用户ID */
  userId?: string;
  /*业务类型 */
  bizType?: string;
  /*业务ID */
  bizId?: string;
  /*文件名称 */
  fileName?: string;
  /*文件路径 */
  filePath?: string;
  /*文件大小(字节) */
  fileSize?: number;
  /*文件类型 */
  fileType?: string;
  /*上传时间 */
  createTime?: Record<string, unknown>;
  /*删除状态 */
  delFlag?: number;
  /*创建人 */
  createBy?: string;
  /*更新人 */
  updateBy?: string;
  /*更新时间 */
  updateTime?: Record<string, unknown>;
}

// Parameter interface
export interface AddDraftParams {
  /*服务单id */
  id?: string;
  /*委托企业ID，在自主模式下为对应处置单位 */
  entrustCompanyId: number;
  /*受委托企业ID */
  onEntrustCompanyId: number;
  /*委托类型(1-增值 2-自主) */
  entrustType: number;
  /*服务类型(1-竞价委托 2-资产处置 3-采购信息) */
  serviceType: number;
  /*联系人 */
  relationUser: string;
  /*状态(1-草稿 2-待审核 3-审核通过 4-审核拒绝 ) */
  status: number;
  /*联系电话 */
  relationPhone: string;
  /*资产名称 */
  assetName: string;
  /*资产编号 */
  assetNo?: string;
  /*资产类型 */
  assetType: number;
  /*资产数量 */
  quantity: Record<string, unknown>;
  /*是否展示实际数量(0:不展示，资产数量列展示以实际数量为准，1：展示实际数量) */
  quantityFlag?: number;
  /*实际数量 */
  actualQuantity?: Record<string, unknown>;
  /*计量单位 */
  unit: string;
  /*使用年限 */
  serviceLife: number;
  /*折旧程度(01-九成新 02-八成新...) */
  depreciationDegree: number;
  /*当前状态(01-在用 02-闲置 03-报废) */
  currentStatus: number;
  /*评估价值 */
  appraisalValue: number;
  /*处置底价 */
  disposalPrice: number;
  /*处置开始时间 */
  disposalStartTime: Record<string, unknown>;
  /*处置结束时间 */
  disposalEndTime: Record<string, unknown>;
  /*省份 */
  provinceCode: string;
  /*城市编码 */
  cityCode: string;
  /*区县编码 */
  districtCode: string;
  /*详细地址 */
  address: string;
  /*付款方式(1-全款 2-分期) */
  paymentMethod: number;
  /*是否含税(0:表示不含税,orther：表示含税率) */
  isTaxIncluded: Record<string, unknown>;
  /*特殊说明 */
  specialNotes?: string;
  /*审核说明 */
  auditOpinion: string;
  /* */
  servicePayType?: number;
  /*封面图片路径 */
  coverImage: string;
  /*附件列表 */
  attachmentList?: AttachmentItem[];
  /*公告名称（采购信息专用） */
  noticeName?: string;
}

// Response interface
export interface AddDraftRes {}

/**
 * 发起处置-委托添加
 * @param {object} params 资产处置-表单数据
 */
export function addDraft(params: AddDraftParams): Promise<AddDraftRes> {
  return defHttp.post({ url: Api.addDraft, params });
}

/**
 * 获取当前位置
 * @param coordinates 经纬度坐标
 */
export const getCurrentLocation = (coordinates: { latitude: string; longitude: string }) =>
  defHttp.post({
    url: Api.getCurrentLocation,
    params: coordinates,
  });

/**
 * 根据坐标获取地址
 * @param coordinates 经纬度坐标
 */
export const getAddressByCoordinates = (coordinates: { latitude: string; longitude: string }) =>
  defHttp.get({
    url: Api.getAddressByCoordinates,
    params: coordinates,
  });

/**
 * 获取计量单位字典
 */
export const getMeasurementUnits = () => defHttp.get({ url: Api.getMeasurementUnits });

/**
 * 获取委托类型字典
 */
export const getEntrustTypes = () => defHttp.get({ url: Api.getEntrustTypes });

/**
 * 获取字典项
 * @param dictCode 字典编码
 */
export const getDictItems = (dictCode: string) => defHttp.get({ url: Api.getDictItems + dictCode });

/**
 * 文件上传地址
 */
export const getUploadUrl = () => Api.uploadFile;
