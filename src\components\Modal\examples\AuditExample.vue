<template>
  <div class="audit-example">
    <h2>审核弹窗使用示例</h2>
    
    <!-- 模拟数据表格 -->
    <a-table :columns="columns" :data-source="mockData" :pagination="false">
      <template #action="{ record }">
        <a-button type="primary" size="small" @click="handleAudit(record)">
          审核
        </a-button>
        <a-button type="link" size="small" @click="handleViewItems(record)">
          查看标的
        </a-button>
        <a-button type="link" size="small" @click="handleViewDetail(record)">
          查看详情
        </a-button>
      </template>
    </a-table>

    <!-- 审核弹窗 -->
    <CommonAuditModal
      v-model:open="auditModalVisible"
      :record="currentAuditRecord"
      @close="handleCloseAuditModal"
      @success="handleAuditComplete"
    />

    <!-- 标的列表弹窗 -->
    <CommonAuctionItemsModal
      v-model:open="itemsModalVisible"
      :record-id="currentRecordId"
      :fetch-api="fetchAuctionItems"
      @close="itemsModalVisible = false"
    />

    <!-- 详情弹窗 -->
    <CommonDetailModal
      v-model:open="detailModalVisible"
      :record-id="currentRecordId"
      :sections="detailSections"
      :fetch-api="fetchDetailData"
      title="委托详情"
      @close="detailModalVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  import { 
    CommonAuditModal, 
    CommonAuctionItemsModal, 
    CommonDetailModal,
    type AuditRecord,
    type AuctionItem,
    type DetailSection
  } from '/@/components/Modal';

  // 弹窗状态
  const auditModalVisible = ref(false);
  const itemsModalVisible = ref(false);
  const detailModalVisible = ref(false);
  const currentAuditRecord = ref<AuditRecord | null>(null);
  const currentRecordId = ref('');

  // 表格列定义
  const columns = [
    { title: '委托单号', dataIndex: 'id', key: 'id' },
    { title: '项目名称', dataIndex: 'projectName', key: 'projectName' },
    { title: '委托类型', dataIndex: 'entrustType', key: 'entrustType' },
    { title: '服务类型', dataIndex: 'serviceType', key: 'serviceType' },
    { title: '状态', dataIndex: 'status', key: 'status' },
    { title: '操作', key: 'action', slots: { customRender: 'action' } },
  ];

  // 模拟数据
  const mockData = [
    {
      id: 'ENT001',
      projectName: '办公设备拍卖',
      entrustType: 1,
      serviceType: 1,
      status: 2,
      relationUser: '张三',
      relationPhone: '13800138000',
      applicantUser: '李四',
      submitTime: '2024-01-15 10:30:00',
    },
    {
      id: 'ENT002',
      projectName: '资产处置项目',
      entrustType: 2,
      serviceType: 2,
      status: 2,
      relationUser: '王五',
      relationPhone: '13900139000',
      applicantUser: '赵六',
      submitTime: '2024-01-16 14:20:00',
    },
  ];

  // 详情配置
  const detailSections: DetailSection[] = [
    {
      key: 'basic',
      title: '基本信息',
      fields: [
        { key: 'id', label: '委托单号' },
        { key: 'projectName', label: '项目名称' },
        { key: 'relationUser', label: '联系人' },
        { key: 'relationPhone', label: '联系电话' },
        { key: 'submitTime', label: '提交时间', type: 'datetime' },
      ]
    },
    {
      key: 'entrust',
      title: '委托信息',
      fields: [
        { 
          key: 'entrustType', 
          label: '委托类型', 
          type: 'tag',
          formatter: (value) => {
            const map = { 1: '增值委托', 2: '自主委托', 3: '供求信息' };
            return map[value] || '未知';
          }
        },
        { 
          key: 'serviceType', 
          label: '服务类型',
          type: 'tag',
          formatter: (value) => {
            const map = { 1: '竞价委托', 2: '资产处置', 3: '采购信息' };
            return map[value] || '未知';
          }
        },
        { 
          key: 'status', 
          label: '审核状态',
          type: 'tag',
          formatter: (value) => {
            const map = { 2: '待审核', 3: '已通过', 4: '已拒绝' };
            return map[value] || '未知';
          }
        },
      ]
    }
  ];

  // 事件处理函数
  function handleAudit(record: any) {
    currentAuditRecord.value = record;
    auditModalVisible.value = true;
  }

  function handleViewItems(record: any) {
    currentRecordId.value = record.id;
    itemsModalVisible.value = true;
  }

  function handleViewDetail(record: any) {
    currentRecordId.value = record.id;
    detailModalVisible.value = true;
  }

  function handleCloseAuditModal() {
    auditModalVisible.value = false;
    currentAuditRecord.value = null;
  }

  function handleAuditComplete() {
    handleCloseAuditModal();
    message.success('审核完成');
    // 这里可以刷新列表数据
  }

  // 模拟API函数
  async function fetchAuctionItems(recordId: string): Promise<AuctionItem[]> {
    console.log('获取标的列表:', recordId);
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            auctionName: '2024年第一期拍卖会',
            itemTitle: '办公设备一批',
            itemType: 1,
            startPrice: 50000,
            appraisalPrice: 80000,
            deposit: 10000,
            quantity: 1,
            unit: '批',
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            address: '科技园南区某大厦',
            description: '包含电脑、打印机、办公桌椅等办公设备',
          },
        ]);
      }, 1000);
    });
  }

  async function fetchDetailData(recordId: string): Promise<any> {
    console.log('获取详情数据:', recordId);
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        const record = mockData.find(item => item.id === recordId);
        resolve(record);
      }, 800);
    });
  }
</script>

<style lang="less" scoped>
  .audit-example {
    padding: 24px;
    
    h2 {
      margin-bottom: 24px;
      color: #262626;
    }
  }
</style>
