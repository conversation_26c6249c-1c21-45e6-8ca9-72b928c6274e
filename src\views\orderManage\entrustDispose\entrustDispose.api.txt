委托处置页面的获取列表数据接口为：/hgy/entrustService/hgyAssetEntrust/queryPageAll

使用JeecgBoot的table组件创建该页面

需要显示的数据有：
序号（当前数据的index）
委托单号 hgyAssetEntrust.entrustOrderId
资产名称 hgyAssetEntrust.assetName
资产编号 hgyAssetEntrust.assetNo
委托单位 hgyEntrustOrder.entrustCompanyName
受委托单位 hgyEntrustOrder.onEntrustCompanyName
资产数量 hgyAssetEntrust.quantity
计量单位 hgyAssetEntrust.unit
资产所在地 hgyAssetEntrust.address
委托时间 hgyAssetEntrust.createTime
保留价 hgyAssetEntrust.disposalPrice
成交价 hgyAssetEntrust.transactionPrice
实际数量 hgyAssetEntrust.actualQuantity
溢价额 hgyAssetEntrust.premiumAmount
溢价率 hgyAssetEntrust.premiumRate
审核状态 hgyAssetEntrust.status(1-草稿 2-待审核 3-审核通过 4-审核拒绝 5-已发布 6-已成交 7-已撤拍)

操作区域中有以下按钮：
编辑、撤拍、删除、报名管理、数企详情、查看详情、竞价记录、结算信息、成交确认书、竞买人列表、工作报告书
操作区域固定右侧
