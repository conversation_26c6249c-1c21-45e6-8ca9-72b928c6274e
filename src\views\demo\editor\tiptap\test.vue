<template>
  <div class="p-4">
    <h2>Tiptap富文本编辑器测试</h2>
    
    <div class="mb-4">
      <a-button @click="getValue">获取内容</a-button>
      <a-button @click="setValue" class="ml-2">设置内容</a-button>
    </div>
    
    <JEditorTiptap
      v-model:value="content"
      height="400px"
      placeholder="请输入内容..."
      @change="handleChange"
    />
    
    <div class="mt-4">
      <h3>内容预览：</h3>
      <div class="border p-4 rounded" v-html="content"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { JEditorTiptap } from '/@/components/Form'

const content = ref('<p>这是初始内容</p>')

const getValue = () => {
  console.log('当前内容:', content.value)
  alert(`内容长度: ${content.value.length}`)
}

const setValue = () => {
  content.value = `<h2>测试标题</h2><p>这是通过代码设置的内容，时间: ${new Date().toLocaleString()}</p>`
}

const handleChange = (value: string) => {
  console.log('内容变化:', value)
}
</script>

<style scoped>
.ml-2 {
  margin-left: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.p-4 {
  padding: 16px;
}

.border {
  border: 1px solid #d9d9d9;
}

.rounded {
  border-radius: 6px;
}
</style>
